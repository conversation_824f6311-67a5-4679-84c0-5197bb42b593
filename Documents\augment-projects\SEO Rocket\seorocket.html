<!DOCTYPE html>
<!-- saved from url=(0044)https://seorocket.chainreaction.ae/dashboard -->
<html lang="en"><plasmo-csui><template shadowrootmode="open"><div id="plasmo-shadow-container" style="z-index: 2147483647; position: relative;"><div id="plasmo-overlay-0" class="plasmo-csui-container" style="display: flex; position: absolute; top: 0px; left: 0px;"></div></div></template></plasmo-csui><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="F7l6IIrYsJZmHwq4Yh4MdT9zjcERSyxnarpYPAbK">
    <link rel="shortcut icon" href="https://seorocket.chainreaction.ae/assets/icon/favicon.ico" type="image/x-icon">
    <title>seorocket</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net/">
    <link href="./seorocket_files/css(1)" rel="stylesheet">

    <!-- Scripts -->
    <link rel="preload" as="style" href="./seorocket_files/app-ba0e91a3.css"><link rel="modulepreload" href="./seorocket_files/app-037c606c.js.download"><link rel="stylesheet" href="./seorocket_files/app-ba0e91a3.css"><script type="module" src="./seorocket_files/app-037c606c.js.download"></script>
    <!-- Custom css -->
    <link rel="stylesheet" href="./seorocket_files/stylesheet.css">
    <link rel="stylesheet" href="./seorocket_files/select2.css">
    <link rel="stylesheet" href="./seorocket_files/style.css">
    <link rel="stylesheet" href="./seorocket_files/toastify.min.css">
    <link rel="stylesheet" href="./seorocket_files/cms.css">
<style>
    .green-outline {
      outline: 1px solid green;
    }

    .red-outline {
      outline: 1px solid red;
    }
  </style><style data-emotion="css" data-s=""></style><style>#rcr-wrapper,
#__ah__serp-side-panel__wrapper,
div[id^=searchElementBar-],
div[id^=__ah__search-stats],
div[id^=ah_g-trends],
#ah_p,
#worker-root {
  --tb-color-primary: #f80;
  --tb-text-color-primary: #333;
  --tb-text-color-secondary: rgba(0, 0, 0, 0.52);
  --tb-text-color-tertiary: rgba(0, 0, 0, 0.32);
  --tb-checkbox-inactive-bg: #ebebeb;
  --tb-checkbox-inactive-dot-color: #cecece;
  --tb-link-color: #00479e;
  --tb-link-color-secondary: #333;
  --tb-border-color-main: rgba(0, 0, 0, 0.08);
  --tb-border-color-secondary: #ccc;
  --tb-userpic-icon-main-color: #d9d9d9;
}

/**
* Sass CSS triangle mixin, create any kind of triangles with ease
* Use: 
* @include triangle(direction,width,height,color);
*/
#rcr-wrapper,
#__ah__serp-side-panel__wrapper,
div[id^=searchElementBar-],
div[id^=__ah__search-stats] {
  --ah-logo-color: #2e3c56;
  --ah-color-main: #f80;
  --ah-color-dr: #7362bf;
  --ah-color-chart: #92bb39;
  --ah-color-text-placeholder: #b6b6b6;
  --ah-color-link-primary: #1a0dab;
  --ah-color-background-hovered: #eee;
  --ah-color-input-primary: #adadad;
  --ah-color-inncative: #ebebeb;
  --ah-google-link-color: #1a0dab;
  --ah-icon-color: #606368;
  --ah-google-link-color-serp: #1a0dab;
  --ah-border-color-primary: rgba(0, 0, 0, 0.08);
  --ah-color-background-hovered-serp: #eee;
  --ah-color-inncative-serp: #ebebeb;
  --ah-modal-background-serp: #fff;
  --ah-color-selected-serp: #ffdbb3;
  --ah-toolbar-bg: #fff;
  --ah-toolbar-border-color: #d9d9d9;
  --ah-toolbar-section-heading-bg: #a6cee3;
  --ah-toolbar-icons-color: #333;
  --tb-userpic-icon-main-color: #d9d9d9;
  --ah-toolbar-secondary-font-color: #333;
  --ah-toolbar-stats-item-border-color: #e2e3e6;
  --ah-toolbar-popover-header-bg: #fff;
  --ah-toolbar-popover-body-bg: #fff;
  --ah-toolbar-popover-header-shadow: #e5e5e8;
  --ah-toolbar-section-item: #f9f9f9;
  --ah-preloader-line-color-secondary: #ebebed;
  --tb-text-color-primary: #333;
  --tb-success-color: #27a765;
  --tb-notice-color: #3288d7;
  --tb-warning-color: #face0d;
  --tb-warning-muted-color: #fce892;
  --tb-error-color: #f13333;
}

#rcr-wrapper,
#__ah__serp-side-panel__wrapper,
div[id^=searchElementBar-],
div[id^=__ah__search-stats],
ytd-app {
  --ah-toolbar-height: 36px;
  --ah-toolbar-stats-height: 26px;
  --ah-toolbar-section-heading-width: 114px;
}

.__ah__full-height {
  height: 100% !important;
}

.__ah__full-width {
  width: 100% !important;
}

.__ah__scrollable {
  overflow-y: auto !important;
}

.__ah__text-center {
  text-align: center !important;
}

.__ah__text-left {
  text-align: left !important;
}

.__ah__text-right-align {
  display: flex !important;
  flex-grow: 1 !important;
  justify-content: flex-end !important;
  align-items: center !important;
  margin-left: 22px !important;
}

.__ah__bold-text {
  font-weight: bold !important;
}

.__ah__flex-1 {
  flex: 1 !important;
}

.__ah__flex-right {
  margin-left: auto !important;
}

.__ah__flex-left {
  margin-right: auto !important;
}

.__ah__flex-top {
  margin-bottom: auto !important;
}

.__ah__flex-bottom {
  margin-top: auto !important;
}

.__ah__display-contents {
  display: contents !important;
}

.__ah__toolbar__icon-wrapper {
  display: flex !important;
  align-items: center !important;
}
.__ah__toolbar__icon-wrapper path {
  fill: var(--tb-text-color-secondary) !important;
}

.__ah__text-wrapper {
  display: inline-flex !important;
  align-items: center !important;
  flex-shrink: 3 !important;
  width: 100% !important;
  font-size: 13px !important;
  color: var(--ah-toolbar-secondary-font-color) !important;
}
.__ah__text-wrapper.__ah__text-monthly-limit {
  justify-content: flex-end !important;
  margin-right: 16px;
}
.__ah__text-wrapper > a {
  margin: 0 4px;
}
.__ah__text-wrapper > * {
  font-size: inherit !important;
}

.__ah__hidden {
  display: none !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

.__ah__margin-right-8 {
  margin-right: 8px;
}

.__ah__margin-right-12 {
  margin-right: 12px;
}

svg.__ah__icon-check {
  opacity: 0.8 !important;
}

.__ah__direction-column {
  flex-direction: column !important;
}

.__ah__direction-row {
  flex-direction: row !important;
}

.__ah__align-self-c {
  align-self: center !important;
}

.__ah__align-self-l {
  align-self: flex-start !important;
}

.__ah__inline-block {
  display: inline-block !important;
}

@media (min-width: 1367px) and (max-width: 1440px) {
  #rcr-wrapper .__ah__line-preloader-wrapper.__ah__line-preloader-t-1 {
    width: 735px !important;
  }
}
@media (max-width: 1440px) {
  .ah_serp-loc-search-container .ah_serp-loc-search-description {
    position: relative !important;
    margin-left: 0 !important;
  }
  .ah_serp-loc-search-container .ah_serp-loc-search-description-header {
    position: absolute !important;
    top: 5px !important;
    right: -5px !important;
  }
  .ah_serp-loc-search-container .ah_serp-loc-search-description-header > span {
    display: none !important;
  }
  .ah_serp-loc-search-container .ah_serp-loc-search-description-body {
    display: none !important;
  }
}
@media (max-width: 1367px) {
  #rcr-wrapper .__ah__line-preloader-wrapper.__ah__line-preloader-t-1 {
    width: 535px !important;
  }
  #rcr-wrapper .ah_web-vitals-header {
    display: none !important;
  }
  #rcr-wrapper .ah_tb-u-banner .ah_feedback-message-buttons button:first-child {
    display: none !important;
  }
}
@media (max-width: 1200px) {
  #rcr-wrapper > #rcr-anchor {
    height: 76px !important;
  }
  #rcr-wrapper > #rcr-anchor:has(> .ah_tb-u) {
    height: 69px !important;
    background-color: #fff !important;
  }
  #rcr-wrapper .ah_tb-logo {
    align-items: start !important;
    padding-top: 11px !important;
  }
  #rcr-wrapper .ah_tb-metrics-wrapper .ah_barstats-wrapper {
    align-items: center !important;
  }
  #rcr-wrapper .ah_tb-metrics-wrapper .ah_barstats-wrapper .ah_barstats-wrapper-box {
    width: 100% !important;
  }
  #rcr-wrapper .ah_tb-metrics-wrapper .ah_barstats-wrapper .ah_barstats-wrapper-box:not(:last-child)::after {
    display: none !important;
  }
  #rcr-wrapper .ah_tb-u {
    position: relative !important;
    height: var(--ah-toolbar-height) !important;
  }
  #rcr-wrapper .ah_tb-u-banner {
    position: absolute !important;
    width: 100% !important;
    top: 100% !important;
    left: 0 !important;
    padding: 0 3px 3px 3px !important;
  }
  #rcr-wrapper .ah_tb-u-banner .ah_feedback-message-buttons button:first-child {
    display: flex !important;
  }
  #rcr-wrapper .ah_barstats-wrapper {
    flex-direction: column !important;
    height: 100% !important;
    justify-content: space-between !important;
  }
  #rcr-wrapper .ah_barstats-wrapper-main {
    height: 100% !important;
  }
  #rcr-wrapper .ah_barstats-wrapper-main::after {
    content: "" !important;
    position: absolute !important;
    top: 0 !important;
    right: 0px !important;
    width: 1px !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.08) !important;
  }
  #rcr-wrapper .ah_barstats-wrapper-main .ah_statsbox {
    flex-direction: column !important;
    align-items: flex-start !important;
  }
  #rcr-wrapper .ah_barstats-wrapper-main .ah_statsbox-item {
    height: 16px !important;
  }
  #rcr-wrapper .ah_web-vitals-header > span {
    display: flex !important;
  }
  #rcr-wrapper .__ah__toolbar__center,
  #rcr-wrapper .__ah__toolbar__right-col {
    height: 100% !important;
    align-items: start !important;
    padding-top: 2px !important;
  }
}
@media (max-width: 640px) {
  .ah_trends {
    display: none !important;
  }
}
.minidiv {
  top: var(--ah-toolbar-height) !important;
}
.minidiv .sfbg {
  height: 102px !important;
}
.minidiv #__ah__search-stats {
  margin-top: 5px !important;
}

.gke0pe #hdtb.ah_search-modes {
  margin-top: 8px !important;
}

#cnt:has(.aAbqZ) {
  padding-top: 34px !important;
}
#cnt:has(.PHj8of .nPDzT > .GKS7s) {
  padding-top: 34px !important;
}

/**
  *  Long search form
  */
.Xx7Mif.E5eFb #__ah__search-stats {
  margin-top: -3px !important;
}

@media (max-width: 1790px) {
  /**
    *  Long ggl search form
    */
  .Xx7Mif.E5eFb .ah_serp-loc-search-container .ah_serp-loc-search-description {
    position: relative !important;
    margin-left: 0 !important;
  }
  .Xx7Mif.E5eFb .ah_serp-loc-search-container .ah_serp-loc-search-description-header {
    position: absolute !important;
    top: 5px !important;
    right: -5px !important;
  }
  .Xx7Mif.E5eFb .ah_serp-loc-search-container .ah_serp-loc-search-description-header > span {
    display: none !important;
  }
  .Xx7Mif.E5eFb .ah_serp-loc-search-container .ah_serp-loc-search-description-body {
    display: none !important;
  }
}
#rcr-wrapper,
#__ah__serp-side-panel__wrapper,
#__ah__search-stats,
div[id^=searchElementBar-] {
  direction: ltr;
}
#rcr-wrapper *,
#__ah__serp-side-panel__wrapper *,
#__ah__search-stats *,
div[id^=searchElementBar-] * {
  direction: ltr;
}

#rcr-wrapper {
  display: none !important;
  position: fixed !important;
  z-index: 1000000102 !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  text-align: left !important;
}
#rcr-wrapper.bottom {
  top: auto !important;
  bottom: 0 !important;
}
#rcr-wrapper.bottom #rcr-anchor {
  box-shadow: 0px -1px 0px 0px var(--ah-toolbar-border-color) !important;
}
#rcr-wrapper:not(.bottom) {
  height: var(--ah-toolbar-height) !important;
}
#rcr-wrapper.active {
  display: block !important;
}
#rcr-wrapper.active:not(.bottom) + * #bluebarRoot {
  position: relative !important;
  top: calc(-1 * var(--ah-toolbar-height)) !important;
}
#rcr-wrapper.active:not(.bottom) + * #bluebarRoot > div {
  top: var(--ah-toolbar-height) !important;
}
#rcr-wrapper.active:not(.bottom) + * .fbTimelineStickyHeader {
  top: calc(var(--ah-toolbar-height) + 43px) !important;
}
#rcr-wrapper.active:not(.bottom) ~ * app-drawer#guide.style-scope.ytd-app {
  top: -92px !important;
}
#rcr-wrapper a > .ac-icon,
#rcr-wrapper a .sr-only {
  display: none;
}
#rcr-wrapper svg {
  width: auto;
  height: auto;
  -webkit-transform: none;
  transform: none;
}

#rcr-anchor {
  position: relative !important;
  width: 100%;
  height: var(--ah-toolbar-height) !important;
  box-shadow: 0px 1px 0px 0px var(--ah-toolbar-border-color) !important;
}
#rcr-anchor.hidden {
  z-index: -1 !important;
}
#rcr-anchor [class*=icon] {
  overflow: visible !important;
}

.__ah__toolbar a {
  text-decoration: none !important;
  color: var(--tb-link-color) !important;
  outline: none !important;
  background-image: none !important;
}
.__ah__toolbar * {
  text-shadow: none !important;
  box-sizing: border-box !important;
  font-family: Arial, Helvetica, sans-serif !important;
  font-size: 14px !important;
  text-transform: none !important;
  text-decoration: none !important;
  letter-spacing: 0 !important;
  line-height: normal !important;
  flex-direction: row !important;
  flex-shrink: 1 !important;
  background-color: transparent !important;
  outline: none !important;
  border-color: transparent;
  box-shadow: none !important;
  text-indent: 0 !important;
}
.__ah__toolbar ul {
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
}
.__ah__toolbar li {
  list-style-type: none !important;
  background: none !important;
  margin: 0 !important;
}
.__ah__toolbar li:before {
  width: 0 !important;
}
.__ah__toolbar svg {
  fill: none !important;
  stroke: none !important;
  pointer-events: all !important;
}

.ah_selected-link-internal {
  background-color: rgb(255, 225, 77) !important;
  box-shadow: 0px 0px 0px 3px rgb(255, 225, 77), 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}
.ah_selected-link-internal > img {
  box-shadow: 0px 0px 0px 3px rgb(255, 225, 77), 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}
.ah_selected-link-external {
  background-color: rgb(255, 225, 77) !important;
  box-shadow: 0px 0px 0px 3px rgb(255, 225, 77), 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}
.ah_selected-link-external > img {
  box-shadow: 0px 0px 0px 3px rgb(255, 225, 77), 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}
.ah_selected-link-internalnofollow {
  background-color: rgb(255, 225, 77) !important;
  box-shadow: 0px 0px 0px 3px rgb(255, 225, 77), 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}
.ah_selected-link-internalnofollow > img {
  box-shadow: 0px 0px 0px 3px rgb(255, 225, 77), 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}
.ah_selected-link-externalnofollow {
  background-color: rgb(255, 225, 77) !important;
  box-shadow: 0px 0px 0px 3px rgb(255, 225, 77), 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}
.ah_selected-link-externalnofollow > img {
  box-shadow: 0px 0px 0px 3px rgb(255, 225, 77), 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}
.ah_selected-link-broken {
  background-color: #ff3333 !important;
  box-shadow: 0px 0px 0px 3px #ff3333, 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}
.ah_selected-link-broken > img {
  box-shadow: 0px 0px 0px 3px #ff3333, 0px 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 2px 3px 3px rgba(0, 0, 0, 0.3) !important;
}

.ah_selected-link-internal,
.ah_selected-link-external,
.ah_selected-link-internalnofollow,
.ah_selected-link-externalnofollow {
  color: #000 !important;
}

.__ah__button {
  height: 26px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 0 10px !important;
  font-size: 14px !important;
  line-height: 18px !important;
  border-radius: 3px !important;
  box-sizing: border-box !important;
  text-decoration: none !important;
  outline: none !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
}
.__ah__button:hover {
  opacity: 0.8 !important;
}
.__ah__button--main {
  background-color: var(--ah-color-main) !important;
  color: #fff !important;
}
.__ah__button--pure {
  background-color: rgba(255, 255, 255, 0) !important;
  color: var(--tb-text-color-primary) !important;
  border: 1px solid var(--ah-border-color-primary) !important;
}

.__ah__backdrop {
  position: fixed !important;
  top: var(--ah-toolbar-height) !important;
  left: 0 !important;
  z-index: 99998 !important;
  width: 100% !important;
  height: 100% !important;
  background: transparent !important;
}
.__ah__backdrop--bottom {
  top: auto !important;
  bottom: var(--ah-toolbar-height) !important;
}

.__ah__popover {
  display: block !important;
  top: calc(var(--ah-toolbar-height) + 10px) !important;
  right: 10px !important;
  position: absolute !important;
  border-radius: 3px !important;
  overflow: hidden !important;
  box-shadow: 0 5px 16px rgba(51, 51, 51, 0.2) !important;
  z-index: 99999 !important;
}
.__ah__popover span {
  color: #333 !important;
}
.__ah__popover--clear {
  top: calc(var(--ah-toolbar-height) - 2px) !important;
  right: 0 !important;
}
.__ah__popover--bottom {
  top: auto !important;
  bottom: calc(var(--ah-toolbar-height) + 10px) !important;
}
.__ah__popover--bottom.__ah__popover--clear {
  bottom: calc(var(--ah-toolbar-height) - 2px) !important;
}
.__ah__popover__header {
  justify-content: space-between !important;
  background: var(--ah-toolbar-popover-header-bg) !important;
  position: relative !important;
  min-height: 60px !important;
  box-shadow: inset 0 -1px 0 var(--ah-toolbar-popover-header-shadow) !important;
  overflow: hidden !important;
}
.__ah__popover__header > svg {
  z-index: 99999;
}
.__ah__popover__header .__ah__popover__header-text {
  margin: 0 10px 0 0 !important;
  color: var(--ah-toolbar-secondary-font-color) !important;
  text-align: right !important;
}
.__ah__popover__btn {
  position: absolute !important;
  right: 20px !important;
  top: 30px !important;
  transform: translateY(-50%) !important;
}

.__ah__popover2__body {
  background: var(--ah-toolbar-popover-body-bg) !important;
  overflow: hidden !important;
}

.__ah__copy-block--disabled {
  cursor: default !important;
}

.ah_toolbar__icon2 {
  width: 14px !important;
  height: 14px !important;
  color: var(--ah-toolbar-icons-color) !important;
  opacity: 1 !important;
  transition: opacity 0.15s linear !important;
  font-size: 16px !important;
  cursor: pointer !important;
}
.ah_toolbar__icon2:hover, .ah_toolbar__icon2.__ah__active {
  opacity: 1 !important;
}

.__ah__line-preloader {
  background-image: linear-gradient(to right, var(--ah-preloader-line-color-secondary) 10%, var(--ah-toolbar-section-item) 25%, var(--ah-preloader-line-color-secondary) 40%);
  background-size: 800px 104px !important;
  animation-name: placeHolderShimmer !important;
  animation-duration: 1s !important;
  animation-timing-function: linear !important;
  animation-iteration-count: infinite !important;
  animation-fill-mode: forwards !important;
  display: inline-block !important;
  width: 100% !important;
  height: 10px !important;
}
.__ah__line-preloader-wrapper {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}
.__ah__line-preloader-wrapper.__ah__line-preloader-t-1 {
  width: 835px !important;
  padding: 0 10px !important;
}
.__ah__line-preloader-wrapper.__ah__line-preloader-t-2 {
  width: 100% !important;
}
.__ah__line-preloader-wrapper.__ah__line-preloader-t-3 {
  height: 26px !important;
  width: 80% !important;
}
.__ah__line-preloader-wrapper.__ah__line-preloader-t-4 {
  align-self: flex-start !important;
  height: 23px !important;
  width: 190px !important;
}

@keyframes placeHolderShimmer {
  0% {
    background-position: -400px 0;
  }
  100% {
    background-position: 300px 0;
  }
}
.ah_serp-tabs-header {
  display: flex !important;
  justify-content: space-between !important;
}
.ah_serp-tabs-header-buttons {
  display: flex !important;
  align-self: flex-start !important;
  margin-top: 12px !important;
}
.ah_serp-tabs-header-tabs {
  display: flex !important;
}
.ah_serp-tabs-header-tab {
  position: relative !important;
  height: auto !important;
  padding: 12px 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin-right: 16px !important;
  color: var(--tb-text-color-secondary) !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  line-height: 18px !important;
  cursor: pointer !important;
}
.ah_serp-tabs-header-tab:hover {
  color: var(--tb-text-color-primary) !important;
}
.ah_serp-tabs-header-tab.ah_serp-tabs-header-tab-active {
  color: var(--tb-text-color-primary) !important;
  box-shadow: inset 0 -3px 0 0 var(--tb-text-color-primary) !important;
}
.ah_serp-tabs-header-tab.ah_serp-tabs-header-tab-disabled {
  color: var(--tb-text-color-tertiary) !important;
  box-shadow: none !important;
  cursor: default !important;
}

.ah_serp-tabs-body {
  margin-top: -1px !important;
  padding: 4px 0 !important;
  border-top: 1px solid var(--tb-border-color-main) !important;
}
.ah_serp-tabs-body-collapsed {
  padding: 8px 0 !important;
}
.ah_serp-tabs-body-kw-items, .ah_serp-tabs-body-ppa-items {
  height: 34px !important;
  display: flex !important;
  align-items: center !important;
}
.ah_serp-tabs-body-kw > div:not(:first-child) {
  border-top: 1px solid var(--tb-border-color-main) !important;
}
.ah_serp-tabs-body-ppa-button {
  padding: 12px 0 !important;
}

.ah_modal-confirmation {
  min-height: auto !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
  background-color: #e5f2fc !important;
  padding: 12px !important;
  border-left: 3px solid #3288d7 !important;
  border-radius: 3px;
  overflow: hidden;
}
.ah_modal-confirmation-buttons {
  margin-top: 12px !important;
  line-height: 18px !important;
}
.ah_modal-confirmation-buttons button {
  padding: 4px 10px;
  border-radius: 3px !important;
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
}
.ah_modal-confirmation-buttons button:not(:last-child) {
  margin-right: 8px;
}
.ah_modal-confirmation span,
.ah_modal-confirmation button {
  font-size: 14px !important;
}
.ah_modal-confirmation button.ah_modal-confirmation-button-link {
  color: #00479e !important;
}
.ah_modal-confirmation button.ah_modal-confirmation-button-link:hover {
  color: #f80 !important;
}
.ah_modal-blocklist {
  min-height: 100px !important;
}

.ah_link2 {
  display: inline-flex !important;
  align-items: center !important;
  color: var(--tb-text-color-secondary) !important;
  text-decoration: none !important;
  font-size: 14px !important;
}
.ah_link2-content {
  display: inline-flex !important;
  word-break: break-all !important;
}
.ah_link2 svg {
  margin-left: 4px !important;
}
.ah_link2-base, .ah_link2-u-base {
  color: var(--tb-link-color) !important;
}
.ah_link2-base:hover, .ah_link2-u-base:hover {
  color: #ff8800 !important;
}
.ah_link2-base:hover svg path, .ah_link2-u-base:hover svg path {
  fill: #ff8800 !important;
}
.ah_link2-base:disabled, .ah_link2-u-base:disabled {
  color: var(--tb-text-color-tertiary) !important;
}
.ah_link2-base svg path, .ah_link2-u-base svg path {
  fill: var(--tb-link-color) !important;
}
.ah_link2-neutral, .ah_link2-u-neutral {
  color: var(--tb-link-color-secondary) !important;
}
.ah_link2-neutral:hover, .ah_link2-u-neutral:hover {
  color: #ff8800 !important;
}
.ah_link2-neutral:hover svg path, .ah_link2-u-neutral:hover svg path {
  fill: #ff8800 !important;
}
.ah_link2-neutral:disabled, .ah_link2-u-neutral:disabled {
  color: var(--tb-text-color-tertiary) !important;
}
.ah_link2-neutral svg path, .ah_link2-u-neutral svg path {
  fill: var(--tb-link-color-secondary) !important;
}
.ah_link2-muted, .ah_link2-u-muted {
  color: var(--tb-text-color-secondary) !important;
}
.ah_link2-muted:hover, .ah_link2-u-muted:hover {
  color: var(--tb-text-color-primary) !important;
}
.ah_link2-muted:hover svg path, .ah_link2-u-muted:hover svg path {
  fill: var(--tb-text-color-primary) !important;
}
.ah_link2-muted:disabled, .ah_link2-u-muted:disabled {
  color: var(--tb-text-color-tertiary) !important;
}
.ah_link2-muted svg path, .ah_link2-u-muted svg path {
  fill: var(--tb-text-color-secondary) !important;
}
.ah_link2-u-base, .ah_link2-u-neutral, .ah_link2-u-muted {
  text-decoration-line: underline;
}
.ah_link2-pure-logo svg {
  margin-left: 0 !important;
}
.ah_link2.ah_link2-margin0 {
  margin: 0 !important;
}
.ah_link2.ah_link2-margin0400 {
  margin: 0 4px 0 0 !important;
}
.ah_link2.ah_link2-margin0004 {
  margin: 0 0 0 4px !important;
}
.ah_link2.ah_link2-fz13 {
  font-size: 13px !important;
}
.ah_link2.ah_link2-fz14 {
  font-size: 14px !important;
}

.ah_btn {
  height: 24px !important;
  width: max-content !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 8px !important;
  border-radius: 3px !important;
  color: var(--tb-text-color-primary) !important;
  font-size: 14px !important;
  cursor: pointer !important;
  border: 1px solid var(--tb-border-color-secondary) !important;
}
.ah_btn:hover {
  border-color: var(--tb-text-color-tertiary) !important;
  z-index: 1 !important;
}
.ah_btn:active {
  background-color: rgba(0, 0, 0, 0.2) !important;
}
.ah_btn-children {
  display: flex !important;
}
.ah_btn.ah_btn-p2 {
  height: 26px !important;
}
.ah_btn.ah_btn-main-color {
  background-color: #f80 !important;
  border-color: transparent !important;
  color: #fff !important;
}
.ah_btn.ah_btn-main-color:hover {
  background-color: #e57a00 !important;
}
.ah_btn.ah_btn-main-color:active {
  background-color: #cc6c00 !important;
}
.ah_btn.ah_btn-main-color path {
  fill: #fff !important;
}
.ah_btn.ah_btn-selected {
  background-color: #ffdbb3 !important;
  border-color: #ffdbb3 !important;
}
.ah_btn.ah_btn-danger {
  color: #f13333 !important;
  border-color: rgba(230, 0, 0, 0.2) !important;
}
.ah_btn.ah_btn-danger:hover {
  border-color: rgba(230, 0, 0, 0.4) !important;
}
.ah_btn.ah_btn-ghost {
  color: rgb(66, 72, 77) !important;
  border: 1px solid transparent !important;
}
.ah_btn.ah_btn-ghost:hover {
  background-color: rgba(0, 0, 0, 0.01) !important;
}
.ah_btn.ah_btn-ghost:active {
  background-color: rgba(0, 0, 0, 0.2) !important;
}
.ah_btn.ah_btn-ghost-muted {
  color: rgba(0, 0, 0, 0.52) !important;
  background-color: transparent !important;
  border: 1px solid transparent !important;
}
.ah_btn.ah_btn-ghost-muted:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}
.ah_btn.ah_btn-ghost-muted:active {
  background-color: rgba(0, 0, 0, 0.2) !important;
}
.ah_btn.ah_btn-sm {
  height: 20px !important;
  padding: 0 9px !important;
}
.ah_btn-selected-close {
  background-color: #ffdbb3;
}
.ah_btn-icon {
  display: flex !important;
  align-items: center !important;
  margin-right: 8px !important;
}
.ah_btn-icon > svg > path {
  fill: var(--tb-text-color-primary) !important;
}
.ah_btn-icon:not(:first-child) {
  margin-right: 0 !important;
  margin-left: 8px !important;
}
.ah_btn-clear {
  display: flex !important;
}
.ah_btn-clear svg {
  width: 13px !important;
  height: 13px !important;
}
.ah_btn:disabled {
  background: var(--ah-background-selected-secondary) !important;
  color: var(--tb-text-color-tertiary) !important;
  cursor: default !important;
  border: 1px solid var(--ah-background-selected-secondary) !important;
}
.ah_btn:disabled:hover {
  background: var(--ah-background-selected-secondary) !important;
  border-color: var(--ah-background-selected-secondary) !important;
}
.ah_btn:disabled path {
  fill: var(--tb-text-color-tertiary) !important;
}
.ah_btn-link {
  text-decoration: none !important;
  transition: 0s !important;
  outline: none !important;
  padding: 0 !important;
  border: none !important;
  cursor: pointer !important;
}
.ah_btn-link svg {
  margin-right: 4px !important;
  margin-left: 0px !important;
}
.ah_btn-link:active {
  opacity: 0.75;
}
.ah_btn-clear-margin {
  margin: 0px !important;
}
.ah_btn > .ah_btn-icon {
  width: 12px;
  height: 12px;
}
.ah_btn-chevron {
  margin-left: 8px;
}
.ah_btn-chevron-reverse {
  transform: rotate(180deg);
}

.ah_btn-control-group {
  display: flex !important;
  overflow: hidden !important;
}
.ah_btn-control-group-selected {
  background-color: #ffdbb3;
}
.ah_btn-control-group .ah_btn {
  border-radius: 0 !important;
  border: 1px solid var(--tb-border-color-secondary) !important;
}
.ah_btn-control-group .ah_btn:first-child {
  border-radius: 3px 0 0 3px !important;
}
.ah_btn-control-group .ah_btn:last-child {
  border-radius: 0 3px 3px 0 !important;
}
.ah_btn-control-group .ah_btn + .ah_btn {
  margin-left: -1px !important;
}

.ah_btn-buttons-group {
  display: flex !important;
}
.ah_btn-buttons-group-selected {
  background-color: #ffdbb3 !important;
}
.ah_btn-buttons-group > * {
  border-radius: 0 !important;
  border: 1px solid var(--tb-border-color-secondary) !important;
}
.ah_btn-buttons-group > *:not(:first-child) {
  margin-left: -1px !important;
}
.ah_btn-buttons-group > *:not(:first-child) .ah_dropdown-list-wrapper {
  margin-left: -1px !important;
}
.ah_btn-buttons-group > *:first-child {
  border-radius: 3px 0 0 3px !important;
}
.ah_btn-buttons-group > *:last-child {
  border-radius: 0 3px 3px 0 !important;
}

.ah_btn-more {
  height: 24px;
}
.ah_btn-more .ah_dropdown {
  height: 100%;
}
.ah_btn-more .ah_dropdown-toggle {
  height: 100%;
  border: none !important;
  border-radius: 0;
}

.ah_btn-context {
  display: flex;
  flex-direction: column;
  position: relative;
}
.ah_btn-context-light .ah_btn {
  background-color: transparent !important;
  border-color: var(--ah-border-color-tertiary) !important;
  color: var(--tb-text-color-primary) !important;
}
.ah_btn-context-light .ah_btn:hover {
  border-color: var(--ah-text-tertiary) !important;
  background-color: transparent !important;
}
.ah_btn-context-light .ah_btn:active {
  background-color: var(--ah-border-color-tertiary) !important;
}
.ah_btn-context-light .ah_btn:disabled {
  border-color: var(--ah-border-color-tertiary) !important;
  background-color: var(--ah-background-selected-secondary) !important;
  color: var(--tb-text-color-tertiary) !important;
  cursor: default !important;
}
.ah_btn-context-light .ah_btn:disabled path {
  fill: var(--tb-text-color-tertiary) !important;
}
.ah_btn-context-light .ah_btn path {
  fill: var(--tb-text-color-primary) !important;
}
.ah_btn-context-selected .ah_btn {
  background-color: var(--ah-background-selected) !important;
  border-color: var(--ah-border-color-selected) !important;
}
.ah_btn-context-selected .ah_btn:hover {
  background-color: var(--ah-background-selected) !important;
  border-color: var(--ah-border-color-selected-hover) !important;
}
.ah_btn-context-selected .ah_btn:active {
  background-color: var(--ah-background-selected-active) !important;
  border-color: var(--ah-border-color-selected-active) !important;
}
.ah_btn-context-drop {
  min-width: 144px;
  top: 100%;
  right: 0;
  position: absolute;
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid var(--tb-border-color-main) !important;
}
.ah_btn-context-drop-description {
  padding: 8px 10px;
  border-bottom: 1px solid var(--tb-border-color-main);
}
.ah_btn-context-drop-description .ah_textwrapper-secondary {
  word-break: break-word !important;
}
.ah_btn-context-drop-body {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
  min-width: 100%;
  width: max-content;
}
.ah_btn-context-drop-item {
  width: 100%;
}
.ah_btn-context-drop-item > button {
  display: flex;
  width: 100%;
  padding: 4px 10px;
  cursor: pointer;
}
.ah_btn-context-drop-item:hover {
  background-color: var(--tb-border-color-main);
}

.ah_tooltip {
  width: fit-content !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
}
.ah_tooltip > svg {
  cursor: pointer !important;
}
.ah_tooltip > svg path {
  fill: rgba(0, 0, 0, 0.32) !important;
}
.ah_tooltip > svg:hover path {
  fill: rgba(0, 0, 0, 0.52) !important;
}
.ah_tooltip-container {
  position: absolute !important;
  left: 0 !important;
  top: 100% !important;
  padding-top: 2px !important;
  z-index: 10001;
}
.ah_tooltip-container-left {
  left: auto !important;
  right: 0 !important;
}
.ah_tooltip-container-top {
  top: auto !important;
  bottom: 100% !important;
  padding-bottom: 2px !important;
}
.ah_tooltip-box {
  width: max-content !important;
  max-width: 230px !important;
  box-shadow: 0px 0 6px rgba(0, 0, 0, 0.08) !important;
  background-color: #fff !important;
  color: #333 !important;
  border-radius: 2px !important;
  padding: 4px 8px !important;
  font-size: 13px !important;
  line-height: 16px !important;
  white-space: pre-line !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
}

.ah_feedback {
  width: 100% !important;
  display: flex !important;
  margin-top: 12px !important;
  padding: 12px 16px !important;
  border-radius: 3px !important;
}
.ah_feedback-message {
  width: 100% !important;
  display: flex !important;
  justify-content: space-between !important;
  column-gap: 8px;
}
.ah_feedback-message-column {
  flex-direction: column;
}
.ah_feedback-message-column .ah_feedback-message-body {
  margin-bottom: 12px !important;
  padding: 0 !important;
}
.ah_feedback-message-column .ah_feedback-message-buttons {
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
  flex-wrap: wrap-reverse !important;
}
.ah_feedback-message-column .ah_feedback-message-buttons button:last-child {
  margin-left: 0 !important;
}
.ah_feedback-message-body {
  display: flex !important;
  padding: 3px 0 !important;
}
.ah_feedback-message-icon {
  margin-right: 8px !important;
}
.ah_feedback-message-text {
  color: #333;
  line-height: 18px !important;
  font-size: 14px !important;
}
.ah_feedback-message-buttons {
  display: flex !important;
  row-gap: 8px !important;
  column-gap: 8px !important;
}

.ah_feedback-i-muted {
  background-color: #e5f2fc !important;
}

.ah_feedback-s-muted {
  background-color: #e7faf0 !important;
}

.ah_feedback-toolbar,
.ah_feedback-serp {
  padding: 4px 12px !important;
}
.ah_feedback-toolbar .ah_feedback-message,
.ah_feedback-serp .ah_feedback-message {
  justify-content: center !important;
}
.ah_feedback-toolbar .ah_feedback-message-body,
.ah_feedback-serp .ah_feedback-message-body {
  padding: 0 !important;
  align-items: center !important;
}
.ah_feedback-toolbar .ah_feedback-message-buttons,
.ah_feedback-serp .ah_feedback-message-buttons {
  align-items: center !important;
}

.ah_feedback-serp {
  width: fit-content !important;
}

.ah_serp-loc-search {
  position: absolute !important;
  height: 48px !important;
  left: calc(100% + 20px) !important;
  top: 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  box-sizing: border-box !important;
  color: #333 !important;
  z-index: 1000 !important;
}
.ah_serp-loc-search-container {
  display: flex !important;
  width: max-content !important;
}
.ah_serp-loc-search-ltr {
  left: 0 !important;
}
.ah_serp-loc-search-ltr .ah_serp-loc-search-container {
  position: absolute;
  right: calc(100% + 10px) !important;
}
.ah_serp-loc-search-find-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 36px !important;
  width: 36px !important;
  border-radius: 50% !important;
  background-color: #f80 !important;
  cursor: pointer !important;
  transition: all 0 !important;
}
.ah_serp-loc-search-find-icon path {
  fill: #fff !important;
}
.ah_serp-loc-search-find-icon:hover {
  opacity: 0.8 !important;
}
.ah_serp-loc-search-description {
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  margin-left: 12px !important;
  font-size: 13px !important;
  line-height: 16px !important;
}
.ah_serp-loc-search-description-header {
  display: flex !important;
  color: var(--tb-text-color-primary) !important;
}
.ah_serp-loc-search-description-header > span {
  padding-right: 2px !important;
}
.ah_serp-loc-search-description-header .ah_serp-loc-search-tooltip path {
  fill: var(--tb-text-color-secondary) !important;
}
.ah_serp-loc-search-description-header .ah_serp-loc-search-tooltip:hover path {
  fill: var(--tb-text-color-secondary) !important;
  fill-opacity: 0.8 !important;
}
.ah_serp-loc-search-description > span {
  color: var(--tb-text-color-secondary) !important;
}
.ah_serp-loc-search-g-input {
  border: 2px solid #f80 !important;
}

[class*=ah_highlight-serp-] {
  color: #fff !important;
  border-radius: 2px;
}
[class*=ah_highlight-serp-] * {
  color: #fff !important;
}

.ah_highlight-serp-1 {
  background-color: #3288d7;
  box-shadow: -4px 0 0 1px #3288d7, 4px 0 0 1px #3288d7;
}

.ah_highlight-serp-2 {
  background-color: #ff8800;
  box-shadow: -4px 0 0 1px #ff8800, 4px 0 0 1px #ff8800;
}

.ah_highlight-serp-3 {
  background-color: #27a765;
  box-shadow: -4px 0 0 1px #27a765, 4px 0 0 1px #27a765;
}

.ah_highlight-serp-4 {
  background-color: #f13333;
  box-shadow: -4px 0 0 1px #f13333, 4px 0 0 1px #f13333;
}

.ah_highlight-serp-5 {
  background-color: #7362bf;
  box-shadow: -4px 0 0 1px #7362bf, 4px 0 0 1px #7362bf;
}

.ah_highlight-serp-6 {
  background-color: #face0d;
  box-shadow: -4px 0 0 1px #face0d, 4px 0 0 1px #face0d;
}

.ah_highlight-serp-7 {
  background-color: #607995;
  box-shadow: -4px 0 0 1px #607995, 4px 0 0 1px #607995;
}

.ah_highlight-serp-8 {
  background-color: #92bb39;
  box-shadow: -4px 0 0 1px #92bb39, 4px 0 0 1px #92bb39;
}

.ah_highlight-serp-9 {
  background-color: #d95090;
  box-shadow: -4px 0 0 1px #d95090, 4px 0 0 1px #d95090;
}

.ah_highlight-serp-10 {
  background-color: #ba9369;
  box-shadow: -4px 0 0 1px #ba9369, 4px 0 0 1px #ba9369;
}

.ah_dropdown {
  max-width: 200px !important;
  position: relative;
}
.ah_dropdown-disabled {
  background-color: var(--tb-checkbox-inactive-bg) !important;
  opacity: 0.5;
}
.ah_dropdown-disabled .ah_dropdown-toggle {
  cursor: default;
}
.ah_dropdown-disabled .ah_dropdown-toggle:active {
  background-color: var(--tb-checkbox-inactive-bg) !important;
}
.ah_dropdown-toggle {
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  border-radius: 3px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid var(--ah-border-color-tertiary) !important;
}
.ah_dropdown-toggle > svg {
  flex-shrink: 0;
}
.ah_dropdown-toggle:hover {
  z-index: 1;
  border-color: var(--tb-text-color-tertiary) !important;
}
.ah_dropdown-toggle:active {
  background-color: rgba(0, 0, 0, 0.2) !important;
}
.ah_dropdown-toggle-colored {
  background-color: #ffdbb3 !important;
}
.ah_dropdown-box {
  display: flex;
  align-items: center;
  max-width: calc(100% - 4px);
}
.ah_dropdown-box-prefix {
  color: #b6b6b6;
}
.ah_dropdown-box span:last-child {
  margin-right: 5px;
  display: block;
  text-overflow: ellipsis !important;
  white-space: nowrap;
  overflow: hidden !important;
}
.ah_dropdown-icon {
  width: 18px;
  height: 14px;
  display: flex;
  flex-shrink: 0;
  background-size: 100% 100%;
  margin-right: 8px;
  box-shadow: inset 0 0 0 1px var(--tb-border-color-main);
}
.ah_dropdown-list {
  padding: 4px 0;
  overflow-x: auto;
}
.ah_dropdown-list-wrapper {
  min-width: 100%;
  margin-top: 2px;
  position: absolute;
  border-radius: 3px;
  background-color: #fff;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
}
.ah_dropdown-list-wrapper-dropup {
  bottom: 27px;
}
.ah_dropdown-item {
  display: flex;
  align-items: center;
  padding: 4px 10px;
  cursor: pointer;
}
.ah_dropdown-item:hover {
  background-color: #f2f2f2 !important;
}
.ah_dropdown-item-active {
  background-color: #ffdbb3 !important;
}
.ah_dropdown-item-focused {
  background-color: #f2f2f2 !important;
}
.ah_dropdown-item-prefix {
  color: #b6b6b6;
}
.ah_dropdown-item-underline {
  width: 100%;
  height: 1px;
  margin: 4px 0;
  background-color: rgba(0, 0, 0, 0.08) !important;
}
.ah_dropdown-search {
  height: 35px;
  display: flex;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2) !important;
}
.ah_dropdown-search-icon {
  display: flex;
  align-self: center;
  position: absolute;
}
.ah_dropdown-search input {
  border: none;
  outline: none;
  width: 100%;
  height: 100%;
}

.ah_dropdown-control-group {
  display: flex;
}
.ah_dropdown-control-group .ah_dropdown-close {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
  margin-left: -1px;
  background-color: #ffdbb3 !important;
  border-radius: 0 3px 3px 0;
  cursor: pointer;
  border: 1px solid var(--ah-border-color-tertiary) !important;
}
.ah_dropdown-control-group .ah_dropdown-close:hover {
  z-index: 1;
  border-color: var(--tb-text-color-tertiary) !important;
}
.ah_dropdown-control-group .ah_dropdown-close:active {
  background-color: rgba(0, 0, 0, 0.2) !important;
}
.ah_dropdown-control-group .ah_dropdown-close-icon {
  width: 12px;
  height: 12px;
}
.ah_dropdown-control-group .ah_dropdown-toggle-selected {
  background-color: #ffdbb3 !important;
  border-radius: 3px 0 0 3px;
}
.ah_dropdown-control-group .ah_dropdown-box {
  max-width: 100%;
}

.ah_dropdown-countryselect .ah_dropdown-list-wrapper {
  z-index: 1;
  min-width: 230px;
}
.ah_dropdown-countryselect .ah_dropdown-list {
  max-height: 108px;
}

.ah_dropdown-medium .ah_dropdown-list-wrapper {
  min-width: 200px;
}
.ah_dropdown-medium .ah_dropdown-list {
  max-height: 260px;
}

.__ah__search-stats__wrapper {
  display: flex !important;
  height: 26px !important;
  flex-direction: row !important;
  justify-content: flex-start !important;
  align-items: center !important;
  margin-left: 17px !important;
  color: #333 !important;
}
.__ah__search-stats__wrapper .__ah__unauthorized-wrapper {
  padding: 5px 0 5px 10px !important;
}
.__ah__search-stats__wrapper .__ah__unauthorized-wrapper span {
  font-size: 10px !important;
}
.__ah__search-stats__wrapper .__ah__unauthorized-wrapper > .__ah__btn {
  height: 25px !important;
  margin: 0 7px !important;
  padding: 0 10px !important;
  font-size: 11px !important;
}
.__ah__search-stats__wrapper .f16 {
  margin-right: 5px !important;
  font-size: 14px !important;
}
.__ah__search-stats__wrapper .seo-login-wrapper {
  font-size: 12px !important;
}
.__ah__search-stats__wrapper .out-of-limit-msg {
  width: auto !important;
}
.__ah__search-stats__field {
  display: flex !important;
  align-items: center !important;
  margin-right: 8px !important;
}
.__ah__search-stats__field span {
  font-size: 14px !important;
}
.__ah__search-stats__field-label {
  margin-right: 4px !important;
  color: var(--tb-text-color-secondary) !important;
}
.__ah__search-stats__field-metric {
  color: var(--tb-text-color-primary) !important;
  font-weight: bold !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp {
  display: flex !important;
  padding: 2px 4px !important;
  font-size: 12px !important;
  font-weight: normal !important;
  line-height: 14px !important;
  border-radius: 3px !important;
  color: #333 !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-0 {
  background-color: #8ad0ab !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-1 {
  background-color: #b2ddb6 !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-2 {
  background-color: #d2e3ab !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-3 {
  background-color: #e8e699 !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-4 {
  background-color: #fce892 !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-5 {
  background-color: #ffda8a !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-6 {
  background-color: #ffc788 !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-7 {
  background-color: #ffbf99 !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-8 {
  background-color: #fdaa9f !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-9 {
  background-color: #f89999 !important;
}
.__ah__search-stats__field-metric .ah-serp-kw-metric-wrp-10 {
  background-color: var(--tb-userpic-icon-main-color) !important;
}
.__ah__search-stats__flag {
  width: 14px !important;
  height: 10px !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-size: contain !important;
  box-shadow: inset 0 0 0 1px var(--tb-border-color-main);
  flex-shrink: 0 !important;
  margin-right: 8px !important;
}

#rcr-wrapper .__ah__report__wrap {
  width: 100% !important;
  padding: 20px 0 !important;
  border-bottom: 1px solid var(--ah-toolbar-stats-item-border-color) !important;
}
#rcr-wrapper .__ah__report--disable-style {
  border: none !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
#rcr-wrapper .__ah__report__inner {
  width: 100% !important;
  overflow: hidden !important;
}
#rcr-wrapper .__ah__report__btn {
  font-size: 14px !important;
  margin-left: 150px;
  margin-top: 15px !important;
  color: var(--tb-link-color) !important;
  background-color: transparent !important;
  outline: none !important;
  cursor: pointer !important;
}
#rcr-wrapper .__ah__toolbar__icon-csv {
  width: 14px !important;
  height: 16px !important;
}
#rcr-wrapper .__ah__toolbar__icon-other {
  display: flex !important;
  height: auto !important;
  width: 35px !important;
  margin: 0 !important;
  position: absolute !important;
  right: 0 !important;
  background-color: var(--ah-toolbar-bg) !important;
  flex-direction: column !important;
}
#rcr-wrapper .__ah__toolbar__icon-other > div {
  display: flex !important;
  flex-shrink: 0 !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 35px !important;
}
#rcr-wrapper .__ah__toolbar__icon-other--popover {
  margin-top: -10px !important;
  margin-right: -10px !important;
}
#rcr-wrapper .__ah__other-top {
  top: 100% !important;
  border-left: 1px solid var(--ah-toolbar-border-color) !important;
  border-bottom: 1px solid var(--ah-toolbar-border-color) !important;
}
#rcr-wrapper .__ah__other-bottom {
  flex-direction: column-reverse !important;
  bottom: 100% !important;
}
#rcr-wrapper .__ah__other {
  justify-content: center !important;
  width: 35px !important;
  margin-left: -12px !important;
}
#rcr-wrapper .ah_web-vitals {
  display: flex !important;
}
#rcr-wrapper .ah_web-vitals-title {
  display: flex;
  align-items: center;
  padding-bottom: 14px;
}
#rcr-wrapper .ah_web-vitals-popup {
  flex-direction: column;
  margin-top: 16px;
}
#rcr-wrapper .ah_web-vitals-popup > .ah_web-vitals-metrics {
  flex-wrap: wrap;
  padding-left: 0 !important;
}
#rcr-wrapper .ah_web-vitals-bar {
  position: relative !important;
}
#rcr-wrapper .ah_web-vitals-bar .ah_web-vitals-btn {
  margin-left: 10px !important;
}
#rcr-wrapper .ah_web-vitals-header-bar {
  display: flex !important;
  margin-right: -2px !important;
  padding-left: 10px !important;
}
#rcr-wrapper .ah_web-vitals-header-bar span {
  font-size: 10px !important;
  line-height: 14px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  color: var(--tb-text-color-secondary) !important;
  font-weight: 700 !important;
}
#rcr-wrapper .ah_web-vitals-header-popup {
  display: flex;
  align-items: center;
}
#rcr-wrapper .ah_web-vitals-metrics {
  display: flex !important;
  padding-left: 10px !important;
}
#rcr-wrapper .ah_web-vitals-item {
  display: flex;
  align-items: center;
  line-height: 16px !important;
}
#rcr-wrapper .ah_web-vitals-item:not(:last-child) {
  margin-right: 8px !important;
}
#rcr-wrapper .ah_web-vitals-label {
  color: var(--tb-text-color-secondary) !important;
}
#rcr-wrapper .ah_web-vitals-value {
  padding: 2px 0 !important;
  border-radius: 3px;
  margin-left: 4px !important;
  flex-shrink: 0 !important;
  color: var(--tb-link-color-secondary) !important;
}
#rcr-wrapper .ah_web-vitals-again {
  margin-left: 8px !important;
}
#rcr-wrapper .ah_web-vitals-again .ah_btn-icon {
  margin-right: 0 !important;
}
#rcr-wrapper .ah_web-vitals-btn {
  padding-left: 10px !important;
  align-self: center !important;
}

.__ah__toolbar {
  display: flex !important;
  flex-direction: row !important;
  justify-content: space-between !important;
  z-index: 9999999 !important;
  box-sizing: border-box !important;
  background: var(--ah-toolbar-bg) !important;
  height: inherit !important;
  position: fixed !important;
  width: 100% !important;
}

.__ah__toolbar * {
  margin-top: 0 !important;
}

.__ah__toolbar__left-col, .__ah__toolbar__right-col, .__ah__toolbar__onpage-action-buttons {
  display: flex !important;
  flex-direction: row !important;
}

.__ah__toolbar__left-col {
  width: 100% !important;
  align-items: center;
}

.__ah__toolbar__center {
  display: flex !important;
  margin-right: 7px !important;
  align-self: center !important;
}

.__ah__toolbar__center svg {
  width: 12px !important;
  height: 12px !important;
}

.__ah__toolbar__center > div {
  display: flex !important;
  align-items: center !important;
}

.__ah__toolbar__right-col {
  margin-left: 12px !important;
}

.__ah__toolbar__right-col > * {
  position: relative !important;
  padding: 0 4px !important;
}

.__ah__toolbar__right-col > *:before {
  content: "" !important;
  position: absolute !important;
  width: 1px !important;
  height: calc(100% - 10px) !important;
  top: 6px !important;
  left: 0 !important;
  background-color: rgba(0, 0, 0, 0.08) !important;
}

.__ah__toolbar__right-col svg {
  width: 12px !important;
  height: 12px !important;
}

.__ah__toolbar__onpage-action-buttons {
  min-width: 98px !important;
  padding-left: 5px !important;
}

.__ah__toolbar__onpage-action-buttons > *:not(.__ah__backdrop):not(.__ah__popover):not(.__ah__other):not(:last-child),
.__ah__toolbar__onpage-action-buttons > .__ah__display-contents > * {
  margin-right: 14px !important;
}

.__ah__toolbar__onpage-action-buttons > *:not(.__ah__backdrop):not(.__ah__popover):not(.__ah__other):last-child {
  margin-right: 5px !important;
}

.__ah__toolbar__right-col, .__ah__toolbar__onpage-action-buttons {
  justify-content: flex-end !important;
  align-items: center !important;
  flex-shrink: 0 !important;
}

.__ah__toolbar__logo:hover {
  opacity: 0.8 !important;
}

.__ah__toolbar__logo > svg {
  height: fit-content !important;
}

.__ah__toolbar__icon {
  font-size: 16px !important;
  cursor: pointer !important;
  z-index: 5;
}

.__ah__toolbar__icon-wrapper {
  display: flex !important;
  justify-content: center !important;
  cursor: pointer !important;
  height: 30px !important;
  width: 30px !important;
  border-radius: 50% !important;
}

.__ah__toolbar__icon-wrapper:hover {
  background-color: var(--ah-border-color-primary) !important;
}

.__ah__toolbar__icon-check path {
  fill: rgb(39, 167, 101) !important;
}

.ah_tb-metrics-wrapper {
  width: 100% !important;
  height: calc(100% - 16px) !important;
  display: flex !important;
  align-items: center !important;
}

.ah_tb-u-content {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.ah_tb-u-button {
  max-width: fit-content !important;
}

.ah_barstats-wrapper {
  display: flex !important;
  height: 16px !important;
}

.ah_barstats-wrapper-main {
  position: relative !important;
}

.ah_barstats-wrapper-main::after {
  content: "" !important;
  position: absolute !important;
  top: -2px !important;
  right: 0 !important;
  width: 1px !important;
  height: 20px !important;
  background-color: rgba(0, 0, 0, 0.08) !important;
}

.ah_barstats-wrapper-main > .ah_statsbox {
  height: 100% !important;
  justify-content: space-between !important;
}

.ah_barstats-wrapper-box {
  display: flex !important;
  height: 16px !important;
  position: relative !important;
}

.ah_barstats-wrapper-box:not(:last-of-type)::after {
  content: "" !important;
  position: absolute !important;
  top: -2px !important;
  right: 0 !important;
  width: 1px !important;
  height: 20px !important;
  background-color: rgba(0, 0, 0, 0.08) !important;
}

.ah_barstats-heading {
  display: flex !important;
  flex-shrink: 0 !important;
  margin-right: -2px !important;
  padding-left: 10px !important;
  position: relative !important;
}

.ah_barstats-heading span {
  font-size: 10px !important;
  line-height: 14px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  color: rgba(0, 0, 0, 0.52) !important;
  color: var(--tb-text-color-secondary) !important;
  font-weight: 700 !important;
}

.ah_barstats-metric-wrapper {
  display: flex;
}

.ah_barstats-metric-subdata {
  margin-left: 4px !important;
  color: var(--tb-text-color-secondary) !important;
}

div[id^=searchElementBar-] {
  /**
  * Sass CSS triangle mixin, create any kind of triangles with ease
  * Use: 
  * @include triangle(direction,width,height,color);
  */
}
div[id^=searchElementBar-] .__ah__search-element-bar__info-wrapper {
  display: flex !important;
  line-height: 14px !important;
}
div[id^=searchElementBar-] .__ah__search-element-bar__heading {
  display: flex !important;
  position: relative !important;
  height: 100% !important;
  max-width: 114px !important;
  padding: 0 7px !important;
  align-items: center !important;
  color: #fff !important;
  font-weight: 400 !important;
  font-size: 10px !important;
  line-height: 12px !important;
  text-transform: uppercase !important;
  background: var(--ah-toolbar-section-heading-bg) !important;
}
div[id^=searchElementBar-] .__ah__search-element-bar__item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: space-between !important;
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  text-align: center !important;
  font-size: 11px !important;
  color: #666 !important;
  font-weight: normal !important;
  padding: 2px 0 !important;
  min-width: 39px !important;
  box-sizing: border-box !important;
  background: var(--ah-toolbar-bg) !important;
  transition: background-color 0.3s !important;
  cursor: pointer !important;
}
div[id^=searchElementBar-] .__ah__search-element-bar__item:hover {
  background-color: var(--ah-toolbar-section-item) !important;
}
div[id^=searchElementBar-] .__ah__search-element-bar__item:hover a.si__value {
  color: #f70 !important;
  text-decoration: none !important;
}
div[id^=searchElementBar-] .__ah__search-element-bar__item:not(:last-child):after {
  content: "" !important;
  position: absolute !important;
  width: 1px !important;
  height: 22px !important;
  top: calc(50% - 11px) !important;
  right: 0 !important;
  background-color: var(--ah-toolbar-stats-item-border-color) !important;
}
div[id^=searchElementBar-] .__ah__search-element-bar__item:last-child {
  border-right: none;
}
div[id^=searchElementBar-] .__ah__search-element-bar__item .si__value {
  font-weight: bold !important;
  color: var(--tb-link-color-secondary) !important;
  font-style: normal !important;
  text-decoration: none !important;
}
div[id^=searchElementBar-] .__ah__search-element-bar__item .si__key {
  text-transform: uppercase !important;
  color: var(--ah-toolbar-select-blocked-color) !important;
}
div[id^=searchElementBar-] .__ah__search-element-bar__last-icon {
  background: var(--ah-toolbar-bg) !important;
  position: relative;
}
div[id^=searchElementBar-] .__ah__search-element-bar__last-icon :before {
  content: "" !important;
  position: absolute !important;
  width: 1px !important;
  height: 22px !important;
  top: calc(50% - 11px) !important;
  left: 0 !important;
  background-color: var(--ah-toolbar-stats-item-border-color) !important;
}
div[id^=searchElementBar-] .__ah__search-element-bar__last-icon svg {
  color: var(--tb-link-color) !important;
}
div[id^=searchElementBar-] .__ah__text-wrapper.__ah__unauthorized-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: var(--ah-toolbar-bg) !important;
  color: var(--ah-toolbar-secondary-font-color) !important;
}
div[id^=searchElementBar-] .__ah__text-wrapper.__ah__unauthorized-wrapper span {
  font-size: 10px !important;
}
div[id^=searchElementBar-] .__ah__text-wrapper.__ah__unauthorized-wrapper > .__ah__btn {
  height: 23px !important;
  margin: 0 7px !important;
  font-size: 10px !important;
}
div[id^=searchElementBar-] .__ah__text-wrapper {
  padding-left: 10px !important;
}
div[id^=searchElementBar-] .__ah__text-wrapper.__ah__toolbar-seo {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 545px;
  height: 100%;
  background: var(--ah-toolbar-bg) !important;
  color: var(--ah-toolbar-secondary-font-color) !important;
}
div[id^=searchElementBar-] .__ah__text-wrapper.__ah__toolbar-seo span {
  opacity: 0.52 !important;
}
div[id^=searchElementBar-] .__ah__search-result__wrapper {
  flex-direction: row !important;
  justify-content: space-between !important;
  align-items: center !important;
  display: inline-flex !important;
  height: 31px !important;
  border: 1px solid #e2e3e6 !important;
}
div[id^=searchElementBar-] .__ah__search-result__icon-wrapper {
  position: relative !important;
  display: flex !important;
  flex-direction: row !important;
  justify-content: center !important;
  align-items: center !important;
  width: 30px !important;
  font-size: 15px !important;
  height: 100% !important;
}
div[id^=searchElementBar-] .__ah__search-result__icon-wrapper:first-child {
  border-right: 1px solid #e2e3e6 !important;
}
div[id^=searchElementBar-] .__ah__search-result__icon-wrapper img {
  height: 15px !important;
}
div[id^=searchElementBar-] .__ah__search-result__icon-wrapper a {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
  font-size: 20px !important;
  color: #4177a6 !important;
}
div[id^=searchElementBar-] .__ah__search-result__stats {
  height: 100% !important;
  display: flex !important;
}
div[id^=searchElementBar-] .__ah__search-result__info-line {
  flex-direction: row !important;
  justify-content: center !important;
  align-items: center !important;
}

div.__ah__serp-position {
  position: absolute !important;
  width: 50px !important;
  left: -62px !important;
  margin-top: 46px !important;
  text-align: right !important;
  font-size: 20px !important;
  color: #808080 !important;
}

div.__ah__serp-position__ltr {
  right: -62px !important;
}

div.__ah__serp-position > div {
  line-height: 21px !important;
}

div.__ah__feature-snippet {
  overflow: visible !important;
}

/**
* Sass CSS triangle mixin, create any kind of triangles with ease
* Use: 
* @include triangle(direction,width,height,color);
*/
.__ah__serp-keywords {
  width: 100% !important;
  min-width: 360px !important;
  font-size: 14px !important;
}

.__ah__serp-keywords ::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 7px;
}

.__ah__serp-keywords ::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}

.__ah__serp-keywords--wrapper {
  position: absolute !important;
  left: 109% !important;
}

.__ah__serp-keywords--head {
  display: flex !important;
  justify-content: space-between !important;
  margin-bottom: 5px !important;
}

.__ah__serp-keywords--title {
  font-weight: bold !important;
  margin: 0 !important;
}

.__ah__serp-keywords--author {
  margin: 0 !important;
  font-size: 13px !important;
  color: #7a7a7a !important;
}

.__ah__serp-keywords--data {
  max-height: 200px !important;
  padding: 6px 10px !important;
  border-radius: 8px !important;
  box-sizing: border-box !important;
  white-space: pre-line !important;
  overflow-y: scroll !important;
  border: 1px solid #dfe1e5 !important;
}

.__ah__serp-keywords--hidden {
  display: none !important;
}

/**
* Sass CSS triangle mixin, create any kind of triangles with ease
* Use: 
* @include triangle(direction,width,height,color);
*/
.__ah__serp-side-panel {
  margin-bottom: 46px !important;
}

.__ah__serp-side-panel__r {
  position: relative !important;
  left: 0 !important;
  margin-bottom: 38px !important;
}

.__ah__serp-side-panel__l {
  position: absolute !important;
  left: 109% !important;
}

.__ah__serp-side-panel__l.__ah__serp-side-panel__ltr {
  right: calc(100% + 60px) !important;
  background-color: transparent !important;
}

.__ah__serp-side-panel__l.__ah__serp-side-panel__ltr .__ah__serp-side-panel {
  background-color: #fff;
  right: 350px;
}

.__ah__serp-side-panel {
  width: 368px !important;
  flex-direction: column !important;
  font-size: 14px !important;
  border-radius: 7px !important;
  position: relative !important;
  border: 1px solid var(--ah-border-color-primary) !important;
}

.__ah__serp-side-panel a {
  text-decoration: none !important;
  color: var(--tb-link-color) !important;
  outline: none !important;
  background-image: none !important;
}

.__ah__serp-side-panel * {
  text-shadow: none !important;
  box-sizing: border-box !important;
  font-family: Arial, Helvetica, sans-serif !important;
  font-size: 14px !important;
  text-transform: none !important;
  text-decoration: none !important;
  letter-spacing: 0 !important;
  line-height: normal !important;
  flex-direction: row !important;
  flex-shrink: 1 !important;
  background-color: transparent !important;
  outline: none !important;
  border-color: transparent;
  box-shadow: none !important;
  text-indent: 0 !important;
}

.__ah__serp-side-panel ul {
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.__ah__serp-side-panel li {
  list-style-type: none !important;
  background: none !important;
  margin: 0 !important;
}

.__ah__serp-side-panel li:before {
  width: 0 !important;
}

.__ah__serp-side-panel svg {
  fill: none !important;
  stroke: none !important;
  pointer-events: all !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header {
  display: flex !important;
  width: 100% !important;
  height: 45px !important;
  justify-content: space-between !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_left {
  align-items: center !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_right {
  align-items: center !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_right > div:not(:last-child),
.__ah__serp-side-panel .__ah__serp-side-panel__header_right button:not(:last-child) {
  margin-right: 16px;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_right > svg {
  margin-left: 16px !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_right .__ah__savedlinks-btn > svg {
  margin-right: 5px !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_right .__ah__savedlinks-btn .ah_icon-hovered {
  display: none !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_right .__ah__savedlinks-btn:hover .ah_icon-hovered {
  display: block !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_right .__ah__savedlinks-btn:hover .ah_icon-main {
  display: none !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_right .__ah__serp-side-panel__hidden-csv {
  display: none !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_logo {
  margin-right: 8px !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header_logo svg > path:nth-child(2) {
  fill: var(--ah-logo-color) !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header * {
  display: flex !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body {
  width: 100% !important;
  flex-direction: column !important;
  padding: 0px 16px !important;
  border-bottom: 1px solid var(--ah-border-color-primary) !important;
  border-top: 1px solid var(--ah-border-color-primary) !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body_toggle {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  height: 50px !important;
  cursor: pointer !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body_toggle > span {
  font-size: 14px !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body_toggle > svg {
  transition: 0.3s all !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body_toggle--rotate {
  transform: rotate(-90deg) !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body_list {
  flex-direction: column !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body_item {
  width: 100% !important;
  line-height: 34px !important;
  font-size: 14px !important;
  border-top: 1px solid var(--ah-border-color-primary) !important;
  width: 100% !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body_item > b {
  font-size: 14px !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body_item:last-of-type {
  border-bottom: 1px solid var(--ah-border-color-primary) !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__body_icons {
  display: flex !important;
  justify-content: space-between !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__header,
.__ah__serp-side-panel .__ah__serp-side-panel__footer {
  padding: 14px 16px !important;
}

.__ah__serp-side-panel .__ah__serp-side-panel__footer {
  width: 100% !important;
  height: 43px !important;
}

.__ah__serp-side-panel__after {
  position: absolute !important;
  top: calc(100% + 12px) !important;
  left: 16px !important;
  color: var(--tb-text-color-secondary) !important;
}

#__ah__serp-side-panel__wrapper {
  z-index: 120 !important;
  background-color: var(--ah-modal-background-serp) !important;
}

/**
* Sass CSS triangle mixin, create any kind of triangles with ease
* Use: 
* @include triangle(direction,width,height,color);
*/
.ah_serpbar {
  width: 670px !important;
  height: 46px !important;
  display: flex !important;
  flex-direction: row !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  margin-left: -4px !important;
  font-size: 12px !important;
  box-sizing: border-box !important;
  border-radius: 8px !important;
}

.ah_serpbar a {
  text-decoration: none !important;
}

.ah_serpbar .ah_serpbar-metric-wrapper {
  position: relative !important;
}

.ah_serpbar .ah_serpbar-metric-subdata {
  position: absolute !important;
  top: calc(100% - 5px) !important;
  padding-left: 4px !important;
  color: var(--tb-text-color-secondary) !important;
}

.ah_serpbar__block {
  display: flex !important;
  flex-direction: row !important;
  height: fit-content !important;
}

.ah_serpbar__block:nth-child(2), .ah_serpbar__block:first-child {
  margin-right: 9px !important;
  position: relative !important;
}

.ah_serpbar__block:first-child::after {
  content: "";
  position: absolute !important;
  right: -5px !important;
  height: 100% !important;
  top: 4px !important;
  height: calc(100% - 8px) !important;
  width: 1px !important;
  background-color: var(--ah-border-color-primary) !important;
}

.ah_serpbar__header {
  display: flex !important;
  padding: 4px 6px !important;
  border-radius: 6px !important;
  font-weight: 700 !important;
  letter-spacing: 0.05em !important;
  cursor: pointer !important;
  transition: all 0 !important;
}

.ah_serpbar__header a {
  font-size: 10px !important;
  line-height: 14px !important;
  text-transform: uppercase !important;
  color: var(--tb-text-color-secondary) !important;
}

.ah_serpbar__header:hover {
  background-color: var(--ah-color-background-hovered-serp) !important;
}

.ah_serpbar__item {
  cursor: pointer !important;
  transition: all 0 !important;
}

.ah_serpbar__item:hover {
  background-color: var(--ah-color-background-hovered-serp) !important;
}

.ah_serpbar__item {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  color: var(--tb-text-color-secondary) !important;
  width: 48px !important;
  line-height: initial !important;
  padding: 4px !important;
  box-sizing: border-box !important;
  border-radius: 6px !important;
}

.ah_serpbar__item-inner {
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
}

.ah_serpbar__item-data {
  color: var(--tb-text-color-primary) !important;
  font-weight: 700 !important;
}

.ah_serpbar__item-chart {
  width: 4px !important;
  height: 30px !important;
  display: flex !important;
  align-items: flex-end !important;
  margin-right: 6px !important;
  background-color: var(--ah-color-inncative-serp) !important;
  border-radius: 2px !important;
  overflow: hidden !important;
}

.ah_serpbar__item-chart--inner-dr {
  width: 4px !important;
  background-color: var(--ah-color-dr) !important;
}

.ah_serpbar__item-chart--inner-ur {
  width: 4px !important;
  background-color: var(--ah-color-chart) !important;
}

.ah_serpbar-add {
  width: 14px !important;
  height: 14px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: flex-start !important;
  margin-left: 6px !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  transition: all 0 !important;
}

.ah_serpbar-add .ah_serpbar-icon > path {
  fill: var(--tb-text-color-tertiary) !important;
}

.ah_serpbar-add .ah_serpbar-icon:hover > path {
  fill: var(--tb-text-color-primary) !important;
}

.ah_serpbar-item-small {
  width: 26px !important;
}

.ah_serpbar-item-small-c {
  width: 40px !important;
}

@media (min-width: 1367px) and (max-width: 1440px) {
  #rcr-wrapper .__ah__line-preloader-wrapper.__ah__line-preloader-t-1 {
    width: 735px !important;
  }
}
@media (max-width: 1440px) {
  .ah_serp-loc-search-container .ah_serp-loc-search-description {
    position: relative !important;
    margin-left: 0 !important;
  }
  .ah_serp-loc-search-container .ah_serp-loc-search-description-header {
    position: absolute !important;
    top: 5px !important;
    right: -5px !important;
  }
  .ah_serp-loc-search-container .ah_serp-loc-search-description-header > span {
    display: none !important;
  }
  .ah_serp-loc-search-container .ah_serp-loc-search-description-body {
    display: none !important;
  }
}
@media (max-width: 1367px) {
  #rcr-wrapper .__ah__line-preloader-wrapper.__ah__line-preloader-t-1 {
    width: 535px !important;
  }
  #rcr-wrapper .ah_web-vitals-header {
    display: none !important;
  }
  #rcr-wrapper .ah_tb-u-banner .ah_feedback-message-buttons button:first-child {
    display: none !important;
  }
}
@media (max-width: 1200px) {
  #rcr-wrapper > #rcr-anchor {
    height: 76px !important;
  }
  #rcr-wrapper > #rcr-anchor:has(> .ah_tb-u) {
    height: 69px !important;
    background-color: #fff !important;
  }
  #rcr-wrapper .ah_tb-logo {
    align-items: start !important;
    padding-top: 11px !important;
  }
  #rcr-wrapper .ah_tb-metrics-wrapper .ah_barstats-wrapper {
    align-items: center !important;
  }
  #rcr-wrapper .ah_tb-metrics-wrapper .ah_barstats-wrapper .ah_barstats-wrapper-box {
    width: 100% !important;
  }
  #rcr-wrapper .ah_tb-metrics-wrapper .ah_barstats-wrapper .ah_barstats-wrapper-box:not(:last-child)::after {
    display: none !important;
  }
  #rcr-wrapper .ah_tb-u {
    position: relative !important;
    height: var(--ah-toolbar-height) !important;
  }
  #rcr-wrapper .ah_tb-u-banner {
    position: absolute !important;
    width: 100% !important;
    top: 100% !important;
    left: 0 !important;
    padding: 0 3px 3px 3px !important;
  }
  #rcr-wrapper .ah_tb-u-banner .ah_feedback-message-buttons button:first-child {
    display: flex !important;
  }
  #rcr-wrapper .ah_barstats-wrapper {
    flex-direction: column !important;
    height: 100% !important;
    justify-content: space-between !important;
  }
  #rcr-wrapper .ah_barstats-wrapper-main {
    height: 100% !important;
  }
  #rcr-wrapper .ah_barstats-wrapper-main::after {
    content: "" !important;
    position: absolute !important;
    top: 0 !important;
    right: 0px !important;
    width: 1px !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.08) !important;
  }
  #rcr-wrapper .ah_barstats-wrapper-main .ah_statsbox {
    flex-direction: column !important;
    align-items: flex-start !important;
  }
  #rcr-wrapper .ah_barstats-wrapper-main .ah_statsbox-item {
    height: 16px !important;
  }
  #rcr-wrapper .ah_web-vitals-header > span {
    display: flex !important;
  }
  #rcr-wrapper .__ah__toolbar__center,
  #rcr-wrapper .__ah__toolbar__right-col {
    height: 100% !important;
    align-items: start !important;
    padding-top: 2px !important;
  }
}
@media (max-width: 640px) {
  .ah_trends {
    display: none !important;
  }
}</style><style>
        .green-outline {
            outline: 1px solid green;
        }
        .red-outline {
            outline: 1px solid red;
        }
    </style><style id="chromane_style">body.chromane_rec_nofollow_link_highlighting_enabled a[rel*="nofollow"] {
  margin-right: 8px;
  margin-left: 8px;
  outline: 2px dotted black !important;
  outline-offset: 2px !important;
}
body.chromane_rec_sponsored_link_highlighting_enabled a[rel*="sponsored"] {
  margin-right: 8px;
  margin-left: 8px;
  outline: 2px dotted black !important;
  outline-color: black;
  outline-offset: 2px !important;
}
body.chromane_rec_ugc_link_highlighting_enabled a[rel*="ugc"] {
  margin-right: 8px;
  margin-left: 8px;
  outline: 2px dotted black !important;
  outline-offset: 2px !important;
}
</style></head>
<!--  -->

<body class="dashboard font-sans antialiased min-h-screen bg-[#F3F2F2] sidemenu-active" data-new-gr-c-s-check-loaded="14.1237.0" data-gr-ext-installed="">
    <!-- Page Heading -->
        <header class="h-[59px] md:h-[88px] border-b border-[#0000001A]  sticky top-0 z-[1] bg-white">
        <div class="px-2 md:ps-[18px] md:pe-[50px] flex justify-between items-center h-full">
            <a href="https://seorocket.chainreaction.ae/dashboard" class="flex">
                <img src="./seorocket_files/logo.svg" width="195">
            </a>

            <div class="flex gap-2">

                <div class="relative" x-data="{ open: false }" @click.outside="open = false" @close.stop="open = false">
    <div @click="open = ! open">
        <img src="./seorocket_files/1" class="rounded-full object-cover border cursor-pointer" width="35" height="35">
    </div>

    <div x-show="open" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95" class="absolute z-50 mt-2 w-48 rounded-md shadow-lg origin-top-right right-0" style="display: none;" @click="open = false">
        <div class="rounded-md ring-1 ring-black ring-opacity-5 py-1 bg-white">
            <a class="block w-full px-4 py-2 text-left text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out" href="https://seorocket.chainreaction.ae/profile">Profile</a>
                        <!-- Authentication -->
                        <form method="POST" action="https://seorocket.chainreaction.ae/logout">
                            <input type="hidden" name="_token" value="F7l6IIrYsJZmHwq4Yh4MdT9zjcERSyxnarpYPAbK" autocomplete="off">
                            <a class="block w-full px-4 py-2 text-left text-sm leading-5 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition duration-150 ease-in-out" href="https://seorocket.chainreaction.ae/logout" onclick="event.preventDefault();
                                                this.closest(&#39;form&#39;).submit();">Log Out</a>
                        </form>
        </div>
    </div>
</div>
                <button toggle-menu-bar="" class="bg-transparent border-0  block md:hidden">
                    <img src="./seorocket_files/menu.svg">
                </button>
                <!-- User Profile -->
                <div class="">

                    <!-- <h2 class="mt-4 text-xl font-semibold">Adnan Akram</h2>
                    <p class="text-gray-600"><EMAIL></p> -->
                </div>


                
                
                            </div>
        </div>
    </header>
    


    <div class="flex relative px-3 md:ps-[112px] py-4 md:py-8 pe-6 md:pe-[50px] duration-300" page-wrapper="">
        <!-- resources/views/layouts/sidebar.blade.php -->
<style>
    nav p {
        color: #BEBEBE;
    }
</style>
<aside side-bar="" class="fixed top-[59px] duration-300 md:top-[88px] left-0 h-[calc(100vh-58px)] md:h-[calc(100vh-88px)] w-[50px] md:w-[73px] pt-[18px] pb-[40px] bg-secondary">
    <div class="flex flex-col justify-between h-full">
        <!-- Navigation Links -->
        <nav class="overflow-hidden">
            <ul class="flex flex-col gap-4 md:gap-6">
                <li class="md:px-[13px] flex" toggle-side-menu-li="">
                    <div toggle-side-menu="" class="flex justify-center w-8 h-8 md:w-[47px] md:h-[47px] duration-300 rounded-xl cursor-pointer">
                        <figure class="flex justify-center items-center">
                            <svg class="w-4 h-4 md:w-[27px] md:h-[27px]" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_908_138" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                <rect x="24" y="24" width="24" height="24" transform="rotate(-180 24 24)" fill="#D9D9D9"></rect>
                                </mask>
                                <g mask="url(#mask0_908_138)">
                                <path d="M7.5 8L7.5 16L11.5 12L7.5 8ZM19 3C19.55 3 20.0208 3.19583 20.4125 3.5875C20.8042 3.97917 21 4.45 21 5L21 19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21L5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19L3 5C3 4.45 3.19583 3.97916 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3L19 3ZM16 5L16 19L19 19L19 5L16 5ZM14 5L5 5L5 19L14 19L14 5Z" fill="#BEBEBE"></path>
                                </g>
                            </svg>
                        </figure>
                    </div>
                </li>
                <li class="md:px-[13px] flex">
                    <a href="https://seorocket.chainreaction.ae/dashboard" class="flex justify-center h-[47px] duration-300 rounded-xl gap-3 items-center">
                        <figure class="flex justify-center items-center w-8 h-full md:w-[47px]">
                            <svg class="w-4 h-4 md:w-[27px] md:h-[27px]" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_774_1502" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                <rect width="24" height="24" fill="#D9D9D9"></rect>
                                </mask>
                                <g mask="url(#mask0_774_1502)">
                                <path d="M6 19H9V13H15V19H18V10L12 5.5L6 10V19ZM4 21V9L12 3L20 9V21H13V15H11V21H4Z" fill="#BEBEBE"></path>
                                </g>
                                </svg>
                                
                        </figure>
                        <p class="text-[#BEBEBE] font-medium text-lg whitespace-nowrap">Real-Time Keywords</p>
                    </a>
                </li>
                <li class="md:px-[13px] flex ">
                    <a href="https://seorocket.chainreaction.ae/google-trends" class="flex justify-center h-[47px] duration-300 rounded-xl gap-3 items-center">
                        <figure class="flex justify-center items-center w-8 h-full md:w-[47px]">
                            <svg class="w-4 h-4 md:w-[27px] md:h-[27px]" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_774_1507" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                <rect width="24" height="24" fill="#D9D9D9"></rect>
                                </mask>
                                <g mask="url(#mask0_774_1507)">
                                <path d="M7.4 16L10.45 12.95L12.45 14.95L16 11.425V13H18V8H13V10H14.575L12.45 12.125L10.45 10.125L6 14.6L7.4 16ZM5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H19C19.55 3 20.0208 3.19583 20.4125 3.5875C20.8042 3.97917 21 4.45 21 5V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5ZM5 19H19V5H5V19Z" fill="#BEBEBE"></path>
                                </g>
                            </svg>
                                
                            
                        </figure>
                        <p class="text-[#BEBEBE] font-medium text-lg whitespace-nowrap">Google Trends</p>
                    </a>
                </li>
                <li class="md:px-[13px] flex">
                    <a href="https://seorocket.chainreaction.ae/keyword-density" class="flex justify-center h-[47px] duration-300 rounded-xl gap-3 items-center">
                        <figure class="flex justify-center items-center w-8 h-full md:w-[47px]">
                            <svg class="w-4 h-4 md:w-[27px] md:h-[27px]" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_774_1512" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                <rect width="24" height="24" fill="#D9D9D9"></rect>
                                </mask>
                                <g mask="url(#mask0_774_1512)">
                                <path d="M18 13.25L20 15.25V20C20 20.55 19.8042 21.0208 19.4125 21.4125C19.0208 21.8042 18.55 22 18 22H4C3.45 22 2.97917 21.8042 2.5875 21.4125C2.19583 21.0208 2 20.55 2 20V6C2 5.45 2.19583 4.97917 2.5875 4.5875C2.97917 4.19583 3.45 4 4 4H9.5C9.36667 4.3 9.26667 4.62083 9.2 4.9625C9.13333 5.30417 9.08333 5.65 9.05 6H4V20H18V13.25ZM19.3 8.9L22.5 12.1L21.1 13.5L17.9 10.3C17.55 10.5 17.175 10.6667 16.775 10.8C16.375 10.9333 15.95 11 15.5 11C14.25 11 13.1875 10.5625 12.3125 9.6875C11.4375 8.8125 11 7.75 11 6.5C11 5.25 11.4375 4.1875 12.3125 3.3125C13.1875 2.4375 14.25 2 15.5 2C16.75 2 17.8125 2.4375 18.6875 3.3125C19.5625 4.1875 20 5.25 20 6.5C20 6.95 19.9333 7.375 19.8 7.775C19.6667 8.175 19.5 8.55 19.3 8.9ZM15.5 9C16.2 9 16.7917 8.75833 17.275 8.275C17.7583 7.79167 18 7.2 18 6.5C18 5.8 17.7583 5.20833 17.275 4.725C16.7917 4.24167 16.2 4 15.5 4C14.8 4 14.2083 4.24167 13.725 4.725C13.2417 5.20833 13 5.8 13 6.5C13 7.2 13.2417 7.79167 13.725 8.275C14.2083 8.75833 14.8 9 15.5 9ZM4 20V6V13V12.7V20Z" fill="#BEBEBE"></path>
                                </g>
                            </svg>
                            
                        </figure>
                        <p class="text-[#BEBEBE] font-medium text-lg whitespace-nowrap">Keyword Research</p>
                    </a>
                </li>
                <li class="md:px-[13px] flex">
                    <a href="https://seorocket.chainreaction.ae/keyword-density?tab=tab-2" class="flex justify-center h-[47px] duration-300 rounded-xl gap-3 items-center">
                        <figure class="flex justify-center items-center w-8 h-full md:w-[47px]">
                            <svg class="w-4 h-4 md:w-[27px] md:h-[27px]" width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <mask id="mask0_777_605" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
                            <rect y="0.902924" width="24" height="24" fill="#D9D9D9"></rect>
                            </mask>
                            <g mask="url(#mask0_777_605)">
                            <path d="M2 21.9029V19.9029H22V21.9029H2ZM3 18.9029V11.9029H6V18.9029H3ZM8 18.9029V6.90292H11V18.9029H8ZM13 18.9029V9.90292H16V18.9029H13ZM18 18.9029V3.90292H21V18.9029H18Z" fill="#BEBEBE"></path>
                            </g>
                        </svg>
                            
                        </figure>
                        <p class="text-[#BEBEBE] font-medium text-lg whitespace-nowrap">Keyword Density</p>
                    </a>
                </li>
                <li class="md:px-[13px] flex">
                    <a href="https://seorocket.chainreaction.ae/keyword-density?tab=tab-3" class="flex justify-center h-[47px] duration-300 rounded-xl gap-3 items-center">
                        <figure class="flex justify-center items-center w-8 h-full md:w-[47px]">
                            <svg class="w-4 h-4 md:w-[27px] md:h-[27px]" width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_777_610" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
                                <rect y="0.902924" width="24" height="24" fill="#D9D9D9"></rect>
                                </mask>
                                <g mask="url(#mask0_777_610)">
                                <path d="M15.45 15.9529L16.55 14.9029L14.45 12.8029C14.6333 12.5196 14.7708 12.2196 14.8625 11.9029C14.9542 11.5863 15 11.2529 15 10.9029C15 9.91959 14.6542 9.09042 13.9625 8.41542C13.2708 7.74042 12.45 7.40292 11.5 7.40292C10.55 7.40292 9.72917 7.74042 9.0375 8.41542C8.34583 9.09042 8 9.91959 8 10.9029C8 11.8863 8.34583 12.7154 9.0375 13.3904C9.72917 14.0654 10.55 14.4029 11.5 14.4029C11.85 14.4029 12.1792 14.3571 12.4875 14.2654C12.7958 14.1738 13.1 14.0363 13.4 13.8529L15.45 15.9529ZM11.5 12.9029C10.95 12.9029 10.4792 12.7071 10.0875 12.3154C9.69583 11.9238 9.5 11.4529 9.5 10.9029C9.5 10.3529 9.69583 9.88209 10.0875 9.49042C10.4792 9.09876 10.95 8.90292 11.5 8.90292C12.0333 8.90292 12.5 9.09876 12.9 9.49042C13.3 9.88209 13.5 10.3529 13.5 10.9029C13.5 11.4529 13.3042 11.9238 12.9125 12.3154C12.5208 12.7071 12.05 12.9029 11.5 12.9029ZM4 18.9029C3.45 18.9029 2.97917 18.7071 2.5875 18.3154C2.19583 17.9238 2 17.4529 2 16.9029V5.90292C2 5.35292 2.19583 4.88209 2.5875 4.49042C2.97917 4.09876 3.45 3.90292 4 3.90292H20C20.55 3.90292 21.0208 4.09876 21.4125 4.49042C21.8042 4.88209 22 5.35292 22 5.90292V16.9029C22 17.4529 21.8042 17.9238 21.4125 18.3154C21.0208 18.7071 20.55 18.9029 20 18.9029H4ZM4 16.9029H20V5.90292H4V16.9029ZM1 21.9029V19.9029H23V21.9029H1Z" fill="#BEBEBE"></path>
                                </g>
                            </svg>
                            
                        </figure>
                        <p class="text-[#BEBEBE] font-medium text-lg whitespace-nowrap">Search Operators</p>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- #bottom -->
        <nav class="overflow-hidden">
            <span class="bg-white mx-auto w-[calc(100%-36px)] h-[1px] block"></span>
            <ul class="flex flex-col gap-3 md:gap-6 pt-4">
                <li class="md:px-[13px] flex ">
                    <a href="https://seorocket.chainreaction.ae/help-center" class="flex justify-center h-[47px] duration-300 rounded-xl gap-3 items-center">
                        <figure class="flex justify-center items-center w-8 h-full md:w-[47px]">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M7.879 5.519C9.05 4.494 10.95 4.494 12.121 5.519C13.293 6.544 13.293 8.206 12.121 9.231C11.918 9.41 11.691 9.557 11.451 9.673C10.706 10.034 10.001 10.672 10.001 11.5V12.25M19 10C19 11.1819 18.7672 12.3522 18.3149 13.4442C17.8626 14.5361 17.1997 15.5282 16.364 16.364C15.5282 17.1997 14.5361 17.8626 13.4442 18.3149C12.3522 18.7672 11.1819 19 10 19C8.8181 19 7.64778 18.7672 6.55585 18.3149C5.46392 17.8626 4.47177 17.1997 3.63604 16.364C2.80031 15.5282 2.13738 14.5361 1.68508 13.4442C1.23279 12.3522 1 11.1819 1 10C1 7.61305 1.94821 5.32387 3.63604 3.63604C5.32387 1.94821 7.61305 1 10 1C12.3869 1 14.6761 1.94821 16.364 3.63604C18.0518 5.32387 19 7.61305 19 10ZM10 15.25H10.008V15.258H10V15.25Z" stroke="#BEBEBE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </figure>
                        <p class="text-[#BEBEBE] font-medium text-lg whitespace-nowrap">Help Center</p>
                    </a>
                </li>
                <li class="md:px-[13px] flex ">
                    <a href="https://seorocket.chainreaction.ae/profile" class="flex justify-center h-[47px] duration-300 rounded-xl gap-3 items-center">
                        <figure class="flex justify-center items-center w-8 h-full md:w-[47px]">
                            <svg class="w-4 h-4 md:w-[27px] md:h-[27px]" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <mask id="mask0_774_1517" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                <rect width="24" height="24" fill="#D9D9D9"></rect>
                                </mask>
                                <g mask="url(#mask0_774_1517)">
                                <path d="M5.85 17.1C6.7 16.45 7.65 15.9375 8.7 15.5625C9.75 15.1875 10.85 15 12 15C13.15 15 14.25 15.1875 15.3 15.5625C16.35 15.9375 17.3 16.45 18.15 17.1C18.7333 16.4167 19.1875 15.6417 19.5125 14.775C19.8375 13.9083 20 12.9833 20 12C20 9.78333 19.2208 7.89583 17.6625 6.3375C16.1042 4.77917 14.2167 4 12 4C9.78333 4 7.89583 4.77917 6.3375 6.3375C4.77917 7.89583 4 9.78333 4 12C4 12.9833 4.1625 13.9083 4.4875 14.775C4.8125 15.6417 5.26667 16.4167 5.85 17.1ZM12 13C11.0167 13 10.1875 12.6625 9.5125 11.9875C8.8375 11.3125 8.5 10.4833 8.5 9.5C8.5 8.51667 8.8375 7.6875 9.5125 7.0125C10.1875 6.3375 11.0167 6 12 6C12.9833 6 13.8125 6.3375 14.4875 7.0125C15.1625 7.6875 15.5 8.51667 15.5 9.5C15.5 10.4833 15.1625 11.3125 14.4875 11.9875C13.8125 12.6625 12.9833 13 12 13ZM12 22C10.6167 22 9.31667 21.7375 8.1 21.2125C6.88333 20.6875 5.825 19.975 4.925 19.075C4.025 18.175 3.3125 17.1167 2.7875 15.9C2.2625 14.6833 2 13.3833 2 12C2 10.6167 2.2625 9.31667 2.7875 8.1C3.3125 6.88333 4.025 5.825 4.925 4.925C5.825 4.025 6.88333 3.3125 8.1 2.7875C9.31667 2.2625 10.6167 2 12 2C13.3833 2 14.6833 2.2625 15.9 2.7875C17.1167 3.3125 18.175 4.025 19.075 4.925C19.975 5.825 20.6875 6.88333 21.2125 8.1C21.7375 9.31667 22 10.6167 22 12C22 13.3833 21.7375 14.6833 21.2125 15.9C20.6875 17.1167 19.975 18.175 19.075 19.075C18.175 19.975 17.1167 20.6875 15.9 21.2125C14.6833 21.7375 13.3833 22 12 22ZM12 20C12.8833 20 13.7167 19.8708 14.5 19.6125C15.2833 19.3542 16 18.9833 16.65 18.5C16 18.0167 15.2833 17.6458 14.5 17.3875C13.7167 17.1292 12.8833 17 12 17C11.1167 17 10.2833 17.1292 9.5 17.3875C8.71667 17.6458 8 18.0167 7.35 18.5C8 18.9833 8.71667 19.3542 9.5 19.6125C10.2833 19.8708 11.1167 20 12 20ZM12 11C12.4333 11 12.7917 10.8583 13.075 10.575C13.3583 10.2917 13.5 9.93333 13.5 9.5C13.5 9.06667 13.3583 8.70833 13.075 8.425C12.7917 8.14167 12.4333 8 12 8C11.5667 8 11.2083 8.14167 10.925 8.425C10.6417 8.70833 10.5 9.06667 10.5 9.5C10.5 9.93333 10.6417 10.2917 10.925 10.575C11.2083 10.8583 11.5667 11 12 11Z" fill="#BEBEBE"></path>
                                </g>
                            </svg>
                                
                                
                            
                        </figure>
                        <p class="text-[#BEBEBE] font-medium text-lg whitespace-nowrap">My Profile</p>
                    </a>
                </li>
                <li class="md:px-[13px] flex ">
                    <form method="POST" action="https://seorocket.chainreaction.ae/logout">
                        <input type="hidden" name="_token" value="F7l6IIrYsJZmHwq4Yh4MdT9zjcERSyxnarpYPAbK" autocomplete="off">                        <a class="flex justify-center h-[47px] duration-300 rounded-xl gap-3 items-center" href="javascript:void(0);" onclick="event.preventDefault(); this.closest(&#39;form&#39;).submit();">
                            <figure class="flex justify-center items-center w-8 h-full md:w-[47px]">
                                <svg class="w-4 h-4 md:w-[27px] md:h-[27px]" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <mask id="mask0_774_1522" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                    <rect width="24" height="24" fill="#D9D9D9"></rect>
                                    </mask>
                                    <g mask="url(#mask0_774_1522)">
                                    <path d="M5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H12V5H5V19H12V21H5ZM16 17L14.625 15.55L17.175 13H9V11H17.175L14.625 8.45L16 7L21 12L16 17Z" fill="#BEBEBE"></path>
                                    </g>
                                </svg>
                                    
                                    
                                    
                                
                            </figure>
                            <p class="text-[#BEBEBE] font-medium text-lg whitespace-nowrap">Sign Out</p>    
                        </a>
                    </form>
                </li>
            </ul>
        </nav>
        <!-- ##bottom -->

    </div>
</aside>
        <div class="w-full">
            
            <!-- Page Content -->
            <main>
                <h2 class="font-medium text-[40px]" data-heading-tag="H2">
        Real-Time <span class="text-primary">Keywords</span>
    </h2>

    <div class="d-flex buttons">
        <div class="item active">
            <input keyword-type="tab-1" class="hidden" type="radio" name="filter" checked="" id="tab-1">
            <label for="tab-1">
                <p class="title">Dashboard</p>
            </label>
        </div>
        <div class="item">
            <input keyword-type="tab-2" class="hidden" type="radio" name="filter" id="tab-2">
            <label for="tab-2">
                <p class="title">Table</p>
            </label>
        </div>
    </div>

    <div tab-name="tab-1" class="content-tab">
        <p style="font-size: 17px; color: #848484;line-height: 1; margin-bottom: 28px;">
            The below-<strong>Visualized heat map</strong> shows the hot-searched keywords by different countries.
            Keywords instantly appear from users' searches!
        </p>
        <iframe class="w-full" loading="lazy" style="border: 0px #ffffff none;" src="./seorocket_files/visualize.html" name="myiFrame" width="100%" height="600px" frameborder="1" marginwidth="0px" marginheight="0px" scrolling="no" allowfullscreen="allowfullscreen" data-rocket-lazyload="fitvidscompatible" data-lazy-src="https://trends.google.com/trends/hottrends/visualize?pn=p36&amp;nrow=2&amp;ncol=5" data-ll-status="loaded"></iframe>
    </div>

    <div tab-name="tab-2" class="content-tab hidden">
        <p style="font-size: 17px; color: #848484;line-height: 1; margin-bottom: 28px;">
            The below table shows the hot-searched keywords by different countries.
            Keywords are refreshed on an hourly basis.
        </p>
        <iframe id="embedded-content" style="/* position:absolute; */width: 100%;height: 1000px;top:0;left:0;bottom:0;right:0;border:0px solid transparent" sandbox="allow-forms allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox" src="./seorocket_files/pubhtml.html"></iframe>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const tabs = document.querySelectorAll('input[name="filter"]');
            const contentTabs = document.querySelectorAll('.content-tab');
            const items = document.querySelectorAll('.item');

            tabs.forEach(tab => {
                tab.addEventListener('change', function () {
                    const selectedTab = tab.getAttribute('keyword-type');
                    items.forEach(item => {
                        item.classList.remove('active');
                    });

                    tab.closest('.item').classList.add('active');

                    contentTabs.forEach(contentTab => {
                        if (contentTab.getAttribute('tab-name') === selectedTab) {
                            contentTab.classList.remove('hidden');
                        } else {
                            contentTab.classList.add('hidden');
                        }
                    });
                });
            });
        });
    </script>
    <style>
        [tab-name] {
            display: block;
        }
        .hidden {
            display: none;
        }
        .d-flex {
            display: flex;
            gap: 10px;
            padding-bottom: 2rem;
            display: -webkit-flex;
        }
        .buttons {
            border-radius: 8px;
            border: 1px solid #D9D9D9;
            background: #F3F3F4;
            width: fit-content;
            padding-bottom: 0;
            margin-top: 15px;
            margin-bottom: 36px;
        }
        .item {
            color: #8B8B8B;
            padding: 10px;
            border-radius: 10px;
            transition: all 0.3s;
            text-decoration: none;
            margin: 0.25rem;
            cursor: pointer;
        }
        .item label {
            cursor: pointer;
        }
        .item .title {
            width: 90px;
            text-align: center;
        }
        .item.active, .item:hover {
            background-color: #fff;
        }
        
    </style>
            </main>
        </div>
        <!-- Sidebar -->


    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            
            toggleMenu_mobile()
            countCharacters__TextField();
        });

        function toggleMenu_mobile() {
            const toggleButton = document.querySelector("[toggle-menu-bar]");

            toggleButton.addEventListener("click", function() {
                document.body.classList.toggle("show-menu-bar");
            });
        }

        function countCharacters__TextField() {
            var elements = document.querySelectorAll('[count-characters]');

            if (elements.length > 0) {
                elements.forEach(function(element) {
                    var maxChars = element.getAttribute('maxlength');

                    element.addEventListener('input', function() {
                        var currentLength = element.value.length;
                        var root = element.closest('[root-characters]');
                        var counter = root.querySelector('[chars-counter]');

                        if (counter) {
                            counter.textContent = currentLength + '/' + maxChars;
                        }
                    });
                });
            }
        }

    </script>
    <script src="./seorocket_files/jquery.js.download"></script>
    <script src="./seorocket_files/select2.js.download"></script>
    <script src="./seorocket_files/toastify-js"></script>
    <script src="./seorocket_files/custom-scripts.js.download"></script>
    


<deepl-input-controller translate="no"><template shadowrootmode="open"><link rel="stylesheet" href="chrome-extension://cofdbpoegempjloogbagkncekinflcnj/build/content.css"><div dir="ltr" style="visibility: initial !important;"><div class="dl-input-translation-container svelte-95aucy"><div></div></div></div></template></deepl-input-controller></body><plasmo-csui id="aitdk-csui"><template shadowrootmode="open"><style>*,:before,:after{box-sizing:border-box;border:0 solid #e5e7eb}:before,:after{--tw-content:""}html{-webkit-text-size-adjust:100%;tab-size:4;font-feature-settings:normal;font-variation-settings:normal;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;line-height:1.5}body{line-height:inherit;margin:0}hr{color:inherit;border-top-width:1px;height:0}abbr:where([title]){text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-feature-settings:inherit;font-variation-settings:inherit;font-family:inherit;font-size:100%;font-weight:inherit;line-height:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button;background-color:#0000;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{margin:0;padding:0;list-style:none}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}[hidden]{display:none}:host,:root{--background:0 0% 100%;--foreground:224 71.4% 4.1%;--card:0 0% 100%;--card-foreground:224 71.4% 4.1%;--popover:0 0% 100%;--popover-foreground:224 71.4% 4.1%;--primary:262.1 83.3% 57.8%;--primary-foreground:210 20% 98%;--secondary:220 14.3% 95.9%;--secondary-foreground:220.9 39.3% 11%;--muted:220 14.3% 95.9%;--muted-foreground:220 8.9% 46.1%;--accent:220 14.3% 95.9%;--accent-foreground:220.9 39.3% 11%;--destructive:0 84.2% 60.2%;--destructive-foreground:210 20% 98%;--border:220 13% 91%;--input:220 13% 91%;--ring:262.1 83.3% 57.8%;--radius:4.2px;z-index:2147483647}.dark{--background:224 71.4% 4.1%;--foreground:210 20% 98%;--card:224 71.4% 4.1%;--card-foreground:210 20% 98%;--popover:224 71.4% 4.1%;--popover-foreground:210 20% 98%;--primary:263.4 70% 50.4%;--primary-foreground:210 20% 98%;--secondary:215 27.9% 16.9%;--secondary-foreground:210 20% 98%;--muted:215 27.9% 16.9%;--muted-foreground:217.9 10.6% 64.9%;--accent:215 27.9% 16.9%;--accent-foreground:210 20% 98%;--destructive:0 62.8% 30.6%;--destructive-foreground:210 20% 98%;--border:215 27.9% 16.9%;--input:215 27.9% 16.9%;--ring:263.4 70% 50.4%}*{border-color:hsl(var(--border))}html{font-size:14px}body{background-color:hsl(var(--background));color:hsl(var(--foreground));font-feature-settings:"rlig" 1,"calt" 1;font-size:14px}*,:before,:after,::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:#3b82f680;--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: }.container{width:100%;margin-left:auto;margin-right:auto;padding-left:28px;padding-right:28px}@media (width>=1400px){.container{max-width:1400px}}.visible{visibility:visible}.invisible{visibility:hidden}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.bottom-0{bottom:0}.left-1\/2{left:50%}.right-0{right:0}.right-2{right:7px}.top-0{top:0}.top-1\/2{top:50%}.top-2{top:7px}.-z-\[1\]{z-index:-1}.z-\[1\]{z-index:1}.z-\[2147483647\]{z-index:2147483647}.mx-auto{margin-left:auto;margin-right:auto}.-ml-\[13px\]{margin-left:-13px}.-mt-\[13px\]{margin-top:-13px}.mt-3{margin-top:10.5px}.mt-5{margin-top:17.5px}.block{display:block}.flex{display:flex}.hidden{display:none}.h-full{height:100%}.w-\[420px\]{width:420px}.w-\[520px\]{width:520px}.w-full{width:100%}@keyframes spin{to{transform:rotate(360deg)}}.animate-spin{animation:1s linear infinite spin}.cursor-pointer{cursor:pointer}.items-center{align-items:center}.justify-center{justify-content:center}.gap-x-2{column-gap:7px}.rounded{border-radius:3.5px}.bg-\[\#635bff\]{--tw-bg-opacity:1;background-color:rgb(99 91 255/var(--tw-bg-opacity))}.bg-\[\#635bff\]\/10{background-color:#635bff1a}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity))}.p-1{padding:3.5px}.p-6{padding:21px}.px-4{padding-left:14px;padding-right:14px}.py-2{padding-top:7px;padding-bottom:7px}.py-2\.5{padding-top:8.75px;padding-bottom:8.75px}.text-\[14px\]{font-size:14px}.text-xl{font-size:17.5px;line-height:24.5px}.font-medium{font-weight:500}.leading-6{line-height:21px}.text-black{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity))}.text-gray-400{--tw-text-opacity:1;color:rgb(156 163 175/var(--tw-text-opacity))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55/var(--tw-text-opacity))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.shadow-2xl{--tw-shadow:0 25px 50px -12px #00000040;--tw-shadow-colored:0 25px 50px -12px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.outline{outline-style:solid}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-duration:.15s;transition-timing-function:cubic-bezier(.4,0,.2,1)}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0))}}.hover\:bg-\[\#635bff\]\/90:hover{background-color:#635bffe6}</style><div id="plasmo-shadow-container" style="z-index: 2147483647; position: relative;"><div id="plasmo-inline" class="plasmo-csui-container" style="display: flex; position: relative; top: 0px; left: 0px;"><div class="aitdk-extension shadow-2xl flex fixed w-[520px] top-0 right-0 bottom-0 bg-white hidden"><svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x absolute right-2 top-2 z-[1] p-1 cursor-pointer text-gray-500"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-loader-circle animate-spin text-gray-400 absolute top-1/2 left-1/2 -ml-[13px] -mt-[13px] -z-[1]"><path d="M21 12a9 9 0 1 1-6.219-8.56"></path></svg></div></div></div></template></plasmo-csui><grammarly-desktop-integration data-grammarly-shadow-root="true"><template shadowrootmode="open"><style>
      div.grammarly-desktop-integration {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select:none;
        user-select:none;
      }

      div.grammarly-desktop-integration:before {
        content: attr(data-content);
      }
    </style><div aria-label="grammarly-integration" role="group" tabindex="-1" class="grammarly-desktop-integration" data-content="{&quot;mode&quot;:&quot;full&quot;,&quot;isActive&quot;:true,&quot;isUserDisabled&quot;:false}"></div></template></grammarly-desktop-integration><style id="igor_ext_nofollow">a[rel~='nofollow'],a[rel~='sponsored'],a[rel~='ugc']{outline:.14em dotted red !important;outline-offset:.2em;}a[rel~='nofollow'] img,a[rel~='sponsored'] img,a[rel~='ugc'] img{outline:2px dotted red !important;outline-offset:.2em;}</style></html>